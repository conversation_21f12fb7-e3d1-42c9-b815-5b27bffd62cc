import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SettingsState {
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    fontSize: 'small' | 'medium' | 'large';
    animations: boolean;
  };
  language: {
    current: 'zh-CN' | 'en-US' | 'ja-JP';
    fallback: 'zh-CN' | 'en-US' | 'ja-JP';
  };
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
    reminders: boolean;
  };
  privacy: {
    analytics: boolean;
    crashReports: boolean;
    locationTracking: boolean;
    dataSharing: boolean;
  };
  performance: {
    hardwareAcceleration: boolean;
    backgroundSync: boolean;
    cacheSize: number;
    maxMemoryUsage: number;
  };
  backup: {
    autoBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    cloudSync: boolean;
    localBackupPath: string;
  };
  accessibility: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
    keyboardNavigation: boolean;
  };
  advanced: {
    debugMode: boolean;
    experimentalFeatures: boolean;
    apiEndpoint: string;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
}

const initialState: SettingsState = {
  theme: {
    mode: 'auto',
    primaryColor: '#87CEEB',
    fontSize: 'medium',
    animations: true,
  },
  language: {
    current: 'zh-CN',
    fallback: 'zh-CN',
  },
  notifications: {
    enabled: true,
    sound: true,
    desktop: true,
    email: false,
    reminders: true,
  },
  privacy: {
    analytics: false,
    crashReports: true,
    locationTracking: false,
    dataSharing: false,
  },
  performance: {
    hardwareAcceleration: true,
    backgroundSync: true,
    cacheSize: 500,
    maxMemoryUsage: 800,
  },
  backup: {
    autoBackup: true,
    backupFrequency: 'weekly',
    cloudSync: false,
    localBackupPath: '',
  },
  accessibility: {
    highContrast: false,
    largeText: false,
    screenReader: false,
    keyboardNavigation: true,
  },
  advanced: {
    debugMode: false,
    experimentalFeatures: false,
    apiEndpoint: '',
    logLevel: 'warn',
  },
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateTheme: (state, action: PayloadAction<Partial<SettingsState['theme']>>) => {
      state.theme = { ...state.theme, ...action.payload };
    },
    updateLanguage: (state, action: PayloadAction<Partial<SettingsState['language']>>) => {
      state.language = { ...state.language, ...action.payload };
    },
    updateNotifications: (state, action: PayloadAction<Partial<SettingsState['notifications']>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    updatePrivacy: (state, action: PayloadAction<Partial<SettingsState['privacy']>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    updatePerformance: (state, action: PayloadAction<Partial<SettingsState['performance']>>) => {
      state.performance = { ...state.performance, ...action.payload };
    },
    updateBackup: (state, action: PayloadAction<Partial<SettingsState['backup']>>) => {
      state.backup = { ...state.backup, ...action.payload };
    },
    updateAccessibility: (state, action: PayloadAction<Partial<SettingsState['accessibility']>>) => {
      state.accessibility = { ...state.accessibility, ...action.payload };
    },
    updateAdvanced: (state, action: PayloadAction<Partial<SettingsState['advanced']>>) => {
      state.advanced = { ...state.advanced, ...action.payload };
    },
    resetSettings: () => {
      return initialState;
    },
    importSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload };
    },
  },
});

export const {
  updateTheme,
  updateLanguage,
  updateNotifications,
  updatePrivacy,
  updatePerformance,
  updateBackup,
  updateAccessibility,
  updateAdvanced,
  resetSettings,
  importSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
