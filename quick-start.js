#!/usr/bin/env node

/**
 * 快速启动脚本 - 解决启动问题
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    log('red', '❌ 需要 Node.js 16.0.0 或更高版本');
    log('yellow', `当前版本: ${nodeVersion}`);
    process.exit(1);
  }
  
  log('green', `✅ Node.js 版本: ${nodeVersion}`);
}

function installDependencies() {
  log('blue', '📦 检查并安装依赖...');
  
  try {
    // 检查根目录依赖
    if (!fs.existsSync('node_modules')) {
      log('cyan', '安装根目录依赖...');
      execSync('npm install', { stdio: 'inherit' });
    }
    
    // 检查客户端依赖
    if (!fs.existsSync('client/node_modules')) {
      log('cyan', '安装客户端依赖...');
      execSync('npm install', { stdio: 'inherit', cwd: 'client' });
    }
    
    log('green', '✅ 依赖检查完成');
  } catch (error) {
    log('red', '❌ 依赖安装失败');
    console.error(error.message);
    process.exit(1);
  }
}

function startDevelopment() {
  log('green', '🚀 启动开发服务器...');
  log('cyan', '应用将在 http://localhost:3000 启动');
  
  try {
    const child = spawn('npm', ['start'], {
      stdio: 'inherit',
      cwd: 'client',
      shell: true
    });
    
    // 处理 Ctrl+C
    process.on('SIGINT', () => {
      log('yellow', '\n正在关闭开发服务器...');
      child.kill('SIGINT');
      process.exit(0);
    });
    
    child.on('close', (code) => {
      if (code !== 0) {
        log('red', `❌ 开发服务器退出，代码: ${code}`);
      }
    });
    
  } catch (error) {
    log('red', '❌ 启动失败');
    console.error(error.message);
  }
}

function main() {
  console.clear();
  log('cyan', '╔══════════════════════════════════════════════════════════════╗');
  log('cyan', '║                    LENGSHANG 快速启动                         ║');
  log('cyan', '║                  解决启动问题的简化版本                        ║');
  log('cyan', '╚══════════════════════════════════════════════════════════════╝');
  console.log();
  
  checkNodeVersion();
  installDependencies();
  startDevelopment();
}

if (require.main === module) {
  main();
}
