import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Link,
  Checkbox,
  FormControlLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import { motion } from 'framer-motion';
import { useDispatch, useSelector } from 'react-redux';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

import { RootState } from '../../store';
import { registerUser } from '../../store/slices/userSlice';
import { useI18n } from '../../i18n';
import { validationUtils } from '@shared/utils';

export const RegisterPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useI18n();
  const { isLoading, error } = useSelector((state: RootState) => state.user);

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
  });

  const [formErrors, setFormErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: '',
  });

  // 表单验证
  const validateForm = () => {
    const errors = {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      agreeTerms: '',
    };

    if (!formData.username) {
      errors.username = t('auth.register.usernameRequired');
    } else if (formData.username.length < 2) {
      errors.username = '用户名至少需要2个字符';
    }

    if (!formData.email) {
      errors.email = t('auth.register.emailRequired');
    } else if (!validationUtils.isEmail(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      errors.password = t('auth.register.passwordRequired');
    } else if (formData.password.length < 6) {
      errors.password = '密码至少需要6个字符';
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = t('auth.register.passwordMismatch');
    }

    if (!formData.agreeTerms) {
      errors.agreeTerms = '请同意服务条款和隐私政策';
    }

    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  // 处理输入变化
  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = field === 'agreeTerms' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // 清除对应字段的错误
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // 处理表单提交
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await dispatch(registerUser({
        username: formData.username,
        email: formData.email,
        password: formData.password,
      }));

      if (registerUser.fulfilled.match(result)) {
        navigate('/');
      }
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #87CEEB 0%, #FFFFFF 100%)',
        p: 2,
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card
          sx={{
            maxWidth: 400,
            width: '100%',
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* 标题 */}
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: 'primary.main',
                  mb: 1,
                }}
              >
                {t('auth.register.title')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('auth.register.subtitle')}
              </Typography>
            </Box>

            {/* 错误提示 */}
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {/* 注册表单 */}
            <Box component="form" onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label={t('auth.register.username')}
                value={formData.username}
                onChange={handleInputChange('username')}
                error={!!formErrors.username}
                helperText={formErrors.username}
                margin="normal"
                autoComplete="username"
                autoFocus
              />

              <TextField
                fullWidth
                label={t('auth.register.email')}
                type="email"
                value={formData.email}
                onChange={handleInputChange('email')}
                error={!!formErrors.email}
                helperText={formErrors.email}
                margin="normal"
                autoComplete="email"
              />

              <TextField
                fullWidth
                label={t('auth.register.password')}
                type="password"
                value={formData.password}
                onChange={handleInputChange('password')}
                error={!!formErrors.password}
                helperText={formErrors.password}
                margin="normal"
                autoComplete="new-password"
              />

              <TextField
                fullWidth
                label={t('auth.register.confirmPassword')}
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange('confirmPassword')}
                error={!!formErrors.confirmPassword}
                helperText={formErrors.confirmPassword}
                margin="normal"
                autoComplete="new-password"
              />

              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.agreeTerms}
                    onChange={handleInputChange('agreeTerms')}
                    color="primary"
                  />
                }
                label={
                  <Typography variant="body2">
                    {t('auth.register.agreeTerms')}{' '}
                    <Link href="#" color="primary">
                      {t('auth.register.termsOfService')}
                    </Link>{' '}
                    {t('auth.register.and')}{' '}
                    <Link href="#" color="primary">
                      {t('auth.register.privacyPolicy')}
                    </Link>
                  </Typography>
                }
                sx={{ mt: 1, mb: 1, alignItems: 'flex-start' }}
              />

              {formErrors.agreeTerms && (
                <Typography variant="body2" color="error" sx={{ mb: 1 }}>
                  {formErrors.agreeTerms}
                </Typography>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isLoading}
                sx={{
                  mt: 2,
                  mb: 2,
                  height: 48,
                  background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
                  },
                }}
              >
                {isLoading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  t('auth.register.registerButton')
                )}
              </Button>

              {/* 登录链接 */}
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {t('auth.register.hasAccount')}{' '}
                  <Link
                    component={RouterLink}
                    to="/login"
                    color="primary"
                    sx={{ fontWeight: 'bold' }}
                  >
                    {t('auth.register.signIn')}
                  </Link>
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </motion.div>
    </Box>
  );
};
