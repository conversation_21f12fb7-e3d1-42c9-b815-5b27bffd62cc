import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, CssBaseline, createTheme, Container, Typography, Box } from '@mui/material';

import { store } from './store';

// 简化的主题
const theme = createTheme({
  palette: {
    primary: { main: '#87CEEB' },
    secondary: { main: '#98FB98' },
  },
});

// 简化的首页
const HomePage: React.FC = () => (
  <Container maxWidth="lg" sx={{ py: 4 }}>
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 3, color: 'primary.main' }}>
        LENGSHANG
      </Typography>
      <Typography variant="h5" color="text.secondary" sx={{ mb: 3 }}>
        跨平台心理分析应用
      </Typography>
      <Typography variant="body1" color="text.secondary">
        应用正在启动中...
      </Typography>
    </Box>
  </Container>
);

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="*" element={<HomePage />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </Provider>
  );
};

export default App;