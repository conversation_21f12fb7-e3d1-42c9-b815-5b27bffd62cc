import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box, CircularProgress, Backdrop } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';

// 布局组件
import { MainLayout } from './components/Layout/MainLayout';
import { AuthLayout } from './components/Layout/AuthLayout';

// 页面组件
import { HomePage } from './pages/HomePage';
import { LoginPage } from './pages/Auth/LoginPage';
import { RegisterPage } from './pages/Auth/RegisterPage';
import { NotesPage } from './pages/Notes/NotesPage';
import { TestsPage } from './pages/Tests/TestsPage';
import { GalleryPage } from './pages/Gallery/GalleryPage';
import { ToolboxPage } from './pages/Toolbox/ToolboxPage';
import { VRPage } from './pages/VR/VRPage';
import { GISPage } from './pages/GIS/GISPage';
import { SettingsPage } from './pages/Settings/SettingsPage';
import { NotFoundPage } from './pages/NotFoundPage';

// Redux
import { RootState } from './store';
import { initializeApp, setLoading } from './store/slices/appSlice';
import { loadUserPreferences } from './store/slices/userSlice';

// 服务
import { initializeDatabase } from './services/database';
import { initializeI18n } from './i18n';

// 样式
import './App.css';

// 页面过渡动画配置
const pageVariants = {
  initial: {
    opacity: 0,
    x: -20,
  },
  in: {
    opacity: 1,
    x: 0,
  },
  out: {
    opacity: 0,
    x: 20,
  },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};

function App() {
  const dispatch = useDispatch();
  const { isLoading, isInitialized, error } = useSelector((state: RootState) => state.app);
  const { isAuthenticated } = useSelector((state: RootState) => state.user);
  const [initError, setInitError] = useState<string | null>(null);

  // 应用初始化
  useEffect(() => {
    const initApp = async () => {
      try {
        dispatch(setLoading(true));

        // 初始化数据库
        await initializeDatabase();
        console.log('Database initialized');

        // 初始化国际化
        await initializeI18n();
        console.log('I18n initialized');

        // 加载用户偏好设置
        dispatch(loadUserPreferences());
        console.log('User preferences loaded');

        // 初始化应用状态
        dispatch(initializeApp());
        console.log('App initialized');

        // 检查更新（仅在Electron环境下）
        if (window.isElectron && !window.isDev) {
          // 这里可以添加检查更新的逻辑
          console.log('Checking for updates...');
        }

      } catch (error) {
        console.error('App initialization failed:', error);
        setInitError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        dispatch(setLoading(false));
      }
    };

    initApp();
  }, [dispatch]);

  // 错误处理
  if (initError) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        p={3}
      >
        <Box mb={2} fontSize="1.5rem" color="error.main">
          应用初始化失败
        </Box>
        <Box color="text.secondary" textAlign="center">
          {initError}
        </Box>
        <Box mt={2}>
          <button onClick={() => window.location.reload()}>
            重新加载
          </button>
        </Box>
      </Box>
    );
  }

  // 加载中状态
  if (isLoading || !isInitialized) {
    return (
      <Backdrop open={true} sx={{ zIndex: 9999 }}>
        <Box display="flex" flexDirection="column" alignItems="center">
          <CircularProgress size={60} />
          <Box mt={2} color="white">
            正在初始化应用...
          </Box>
        </Box>
      </Backdrop>
    );
  }

  return (
    <Box className="app" sx={{ height: '100vh', overflow: 'hidden' }}>
      <AnimatePresence mode="wait">
        <Routes>
          {/* 认证路由 */}
          {!isAuthenticated ? (
            <Route path="/*" element={
              <AuthLayout>
                <Routes>
                  <Route path="/login" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <LoginPage />
                    </motion.div>
                  } />
                  <Route path="/register" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <RegisterPage />
                    </motion.div>
                  } />
                  <Route path="*" element={<Navigate to="/login" replace />} />
                </Routes>
              </AuthLayout>
            } />
          ) : (
            /* 主应用路由 */
            <Route path="/*" element={
              <MainLayout>
                <Routes>
                  <Route path="/" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <HomePage />
                    </motion.div>
                  } />
                  <Route path="/notes/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <NotesPage />
                    </motion.div>
                  } />
                  <Route path="/tests/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <TestsPage />
                    </motion.div>
                  } />
                  <Route path="/gallery/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <GalleryPage />
                    </motion.div>
                  } />
                  <Route path="/toolbox/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <ToolboxPage />
                    </motion.div>
                  } />
                  <Route path="/vr/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <VRPage />
                    </motion.div>
                  } />
                  <Route path="/gis/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <GISPage />
                    </motion.div>
                  } />
                  <Route path="/settings/*" element={
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <SettingsPage />
                    </motion.div>
                  } />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </MainLayout>
            } />
          )}
        </Routes>
      </AnimatePresence>
    </Box>
  );
}

export default App;
