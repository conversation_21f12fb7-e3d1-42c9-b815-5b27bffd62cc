import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { SnackbarProvider } from 'notistack';

import { store } from './store';

// 简化的主题
const theme = createTheme({
  palette: {
    primary: { main: '#87CEEB' },
    secondary: { main: '#98FB98' },
  },
  shape: {
    borderRadius: 12,
  },
});

// 功能卡片数据
const features = [
  {
    title: '心理笔记',
    description: '记录日常心情和想法',
    icon: '📝',
    color: '#87CEEB',
  },
  {
    title: '心理测试',
    description: '专业的心理评估工具',
    icon: '🧪',
    color: '#98FB98',
  },
  {
    title: '心理相册',
    description: '图片情绪标记和管理',
    icon: '📸',
    color: '#FFB6C1',
  },
  {
    title: '百宝箱',
    description: '实用的心理健康工具',
    icon: '🛠️',
    color: '#DDA0DD',
  },
  {
    title: 'VR体验',
    description: '沉浸式虚拟现实场景',
    icon: '🥽',
    color: '#F0E68C',
  },
  {
    title: 'GIS分析',
    description: '地理位置情绪分析',
    icon: '🗺️',
    color: '#FFA07A',
  },
];

// 主页组件
const HomePage: React.FC = () => (
  <div>
    {/* 导航栏 */}
    <AppBar position="static" sx={{ bgcolor: 'primary.main' }}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          LENGSHANG 心理分析应用
        </Typography>
      </Toolbar>
    </AppBar>

    {/* 主要内容 */}
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 欢迎区域 */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h2"
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2,
          }}
        >
          LENGSHANG
        </Typography>
        <Typography variant="h5" color="text.secondary" sx={{ mb: 3 }}>
          跨平台心理分析应用
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
          一款融合前沿技术与心理学理论的全面心理健康工具，
          帮助您更好地了解和管理自己的心理状态。
        </Typography>
      </Box>

      {/* 功能卡片网格 */}
      <Grid container spacing={3}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={4} key={feature.title}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  transform: 'translateY(-4px)',
                },
              }}
            >
              <CardContent sx={{ flex: 1, textAlign: 'center', p: 3 }}>
                {/* 功能图标 */}
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: feature.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                    fontSize: '2.5rem',
                    boxShadow: `0 4px 12px ${feature.color}40`,
                  }}
                >
                  {feature.icon}
                </Box>

                {/* 功能信息 */}
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {feature.title}
                </Typography>

                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ lineHeight: 1.5, mb: 2 }}
                >
                  {feature.description}
                </Typography>

                <Button
                  variant="contained"
                  size="small"
                  sx={{
                    bgcolor: feature.color,
                    '&:hover': {
                      bgcolor: feature.color,
                      filter: 'brightness(0.9)',
                    },
                  }}
                  onClick={() => alert(`${feature.title} 功能正在开发中...`)}
                >
                  立即体验
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 底部信息 */}
      <Box
        sx={{
          mt: 6,
          p: 3,
          borderRadius: 2,
          background: 'linear-gradient(135deg, rgba(135, 206, 235, 0.1), rgba(152, 251, 152, 0.1))',
          border: '1px solid rgba(135, 206, 235, 0.3)',
          textAlign: 'center',
        }}
      >
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
          🌟 应用特色
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>🌐 跨平台支持</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Web、Desktop一体化解决方案
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>🧠 专业心理学</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              基于科学的心理测试和分析
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>🎨 现代化设计</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Material Design 3.0设计语言
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Container>
  </div>
);

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="*" element={<HomePage />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </Provider>
  );
};

export default App;