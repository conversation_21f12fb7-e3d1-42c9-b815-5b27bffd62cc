import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { 
  ThemeProvider, 
  CssBaseline, 
  createTheme,
} from '@mui/material';
import { SnackbarProvider } from 'notistack';

import { store } from './store';
import { MainLayout } from './components/Layout/MainLayout';

// 页面组件导入
import { HomePage } from './pages/HomePage';
import { NotesPage } from './pages/Notes/NotesPage';
import { TestsPage } from './pages/Tests/TestsPage';
import { GalleryPage } from './pages/Gallery/GalleryPage';
import { ToolboxPage } from './pages/Toolbox/ToolboxPage';
import { VRPage } from './pages/VR/VRPage';
import { GISPage } from './pages/GIS/GISPage';
import { SettingsPage } from './pages/Settings/SettingsPage';

import './App.css';

// 简化的主题
const theme = createTheme({
  palette: {
    primary: {
      main: '#87CEEB',
    },
    secondary: {
      main: '#98FB98',
    },
  },
  shape: {
    borderRadius: 12,
  },
});

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SnackbarProvider 
          maxSnack={3}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <Router>
            <div className="app">
              <Routes>
                <Route path="/" element={<MainLayout><HomePage /></MainLayout>} />
                <Route path="/notes" element={<MainLayout><NotesPage /></MainLayout>} />
                <Route path="/tests" element={<MainLayout><TestsPage /></MainLayout>} />
                <Route path="/gallery" element={<MainLayout><GalleryPage /></MainLayout>} />
                <Route path="/toolbox" element={<MainLayout><ToolboxPage /></MainLayout>} />
                <Route path="/vr" element={<MainLayout><VRPage /></MainLayout>} />
                <Route path="/gis" element={<MainLayout><GISPage /></MainLayout>} />
                <Route path="/settings" element={<MainLayout><SettingsPage /></MainLayout>} />
                <Route path="*" element={<div>页面未找到</div>} />
              </Routes>
            </div>
          </Router>
        </SnackbarProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
