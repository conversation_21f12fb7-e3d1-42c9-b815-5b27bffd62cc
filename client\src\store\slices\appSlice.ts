import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 应用状态接口
interface AppState {
  isLoading: boolean;
  isInitialized: boolean;
  error: string | null;
  version: string;
  platform: string;
  isOnline: boolean;
  lastSyncTime: Date | null;
  notifications: Notification[];
  performance: {
    memoryUsage: number;
    cpuUsage: number;
    fps: number;
  };
  features: {
    vr: boolean;
    gis: boolean;
    cloudSync: boolean;
    offlineMode: boolean;
  };
}

// 通知接口
interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: {
    label: string;
    action: string;
  }[];
}

// 初始状态
const initialState: AppState = {
  isLoading: false,
  isInitialized: false,
  error: null,
  version: '1.0.0',
  platform: typeof window !== 'undefined' ? window.navigator.platform : 'unknown',
  isOnline: typeof window !== 'undefined' ? window.navigator.onLine : true,
  lastSyncTime: null,
  notifications: [],
  performance: {
    memoryUsage: 0,
    cpuUsage: 0,
    fps: 60,
  },
  features: {
    vr: false,
    gis: true,
    cloudSync: false,
    offlineMode: true,
  },
};

// 创建slice
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // 设置初始化状态
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },

    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },

    // 初始化应用
    initializeApp: (state) => {
      state.isInitialized = true;
      state.isLoading = false;
      state.error = null;
      
      // 检测功能支持
      if (typeof window !== 'undefined') {
        // 检测WebXR支持
        state.features.vr = 'xr' in navigator;
        
        // 检测地理位置支持
        state.features.gis = 'geolocation' in navigator;
        
        // 检测Service Worker支持
        state.features.offlineMode = 'serviceWorker' in navigator;
      }
    },

    // 设置在线状态
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },

    // 更新同步时间
    updateSyncTime: (state) => {
      state.lastSyncTime = new Date();
    },

    // 添加通知
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // 限制通知数量
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },

    // 标记通知为已读
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },

    // 删除通知
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },

    // 清除所有通知
    clearAllNotifications: (state) => {
      state.notifications = [];
    },

    // 标记所有通知为已读
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },

    // 更新性能数据
    updatePerformance: (state, action: PayloadAction<Partial<AppState['performance']>>) => {
      state.performance = {
        ...state.performance,
        ...action.payload,
      };
    },

    // 更新功能状态
    updateFeatures: (state, action: PayloadAction<Partial<AppState['features']>>) => {
      state.features = {
        ...state.features,
        ...action.payload,
      };
    },

    // 设置版本
    setVersion: (state, action: PayloadAction<string>) => {
      state.version = action.payload;
    },

    // 重置应用状态
    resetApp: (state) => {
      return {
        ...initialState,
        platform: state.platform,
        version: state.version,
        features: state.features,
      };
    },
  },
});

// 导出actions
export const {
  setLoading,
  setInitialized,
  setError,
  clearError,
  initializeApp,
  setOnlineStatus,
  updateSyncTime,
  addNotification,
  markNotificationAsRead,
  removeNotification,
  clearAllNotifications,
  markAllNotificationsAsRead,
  updatePerformance,
  updateFeatures,
  setVersion,
  resetApp,
} = appSlice.actions;

// 选择器
export const selectApp = (state: { app: AppState }) => state.app;
export const selectIsLoading = (state: { app: AppState }) => state.app.isLoading;
export const selectIsInitialized = (state: { app: AppState }) => state.app.isInitialized;
export const selectError = (state: { app: AppState }) => state.app.error;
export const selectIsOnline = (state: { app: AppState }) => state.app.isOnline;
export const selectNotifications = (state: { app: AppState }) => state.app.notifications;
export const selectUnreadNotifications = (state: { app: AppState }) => 
  state.app.notifications.filter(n => !n.read);
export const selectPerformance = (state: { app: AppState }) => state.app.performance;
export const selectFeatures = (state: { app: AppState }) => state.app.features;

// 导出reducer
export default appSlice.reducer;
