import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Slider,
  Chip,
  FormControl,
  InputLabel,
  OutlinedInput,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Close as CloseIcon,
  LocationOn as LocationIcon,
  WbSunny as WeatherIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';

import { PsychologyNote, EmotionTag, GeoLocation, WeatherInfo } from '@shared/types';
import { RootState } from '../../store';
import { DatabaseService } from '../../services/database';
import { addNotification } from '../../store/slices/appSlice';
import { EMOTION_TAGS } from '@shared/constants';
import { stringUtils } from '@shared/utils';

interface NoteEditorProps {
  open: boolean;
  note?: PsychologyNote | null;
  onClose: () => void;
  onSave: () => void;
}

export const NoteEditor: React.FC<NoteEditorProps> = ({
  open,
  note,
  onClose,
  onSave,
}) => {
  const dispatch = useDispatch();
  const { profile } = useSelector((state: RootState) => state.user);
  
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    mood: 5,
    emotion: [] as EmotionTag[],
    tags: [] as string[],
    location: null as GeoLocation | null,
    weather: null as WeatherInfo | null,
  });
  
  const [saving, setSaving] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [gettingLocation, setGettingLocation] = useState(false);
  const [gettingWeather, setGettingWeather] = useState(false);

  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title,
        content: note.content,
        mood: note.mood,
        emotion: note.emotion || [],
        tags: note.tags || [],
        location: note.location || null,
        weather: note.weather || null,
      });
    } else {
      setFormData({
        title: '',
        content: '',
        mood: 5,
        emotion: [],
        tags: [],
        location: null,
        weather: null,
      });
    }
  }, [note, open]);

  const handleInputChange = (field: string) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleMoodChange = (event: Event, newValue: number | number[]) => {
    setFormData(prev => ({
      ...prev,
      mood: newValue as number,
    }));
  };

  const handleEmotionToggle = (emotionTag: typeof EMOTION_TAGS[0]) => {
    const emotion: EmotionTag = {
      id: emotionTag.id,
      name: emotionTag.name,
      color: emotionTag.color,
      intensity: 5, // 默认强度
    };

    setFormData(prev => {
      const exists = prev.emotion.find(e => e.id === emotion.id);
      if (exists) {
        return {
          ...prev,
          emotion: prev.emotion.filter(e => e.id !== emotion.id),
        };
      } else {
        return {
          ...prev,
          emotion: [...prev.emotion, emotion],
        };
      }
    });
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleGetLocation = async () => {
    try {
      setGettingLocation(true);
      
      if (!navigator.geolocation) {
        throw new Error('浏览器不支持地理定位');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000,
        });
      });

      const location: GeoLocation = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        address: '当前位置',
      };

      setFormData(prev => ({ ...prev, location }));
      
      dispatch(addNotification({
        type: 'success',
        title: '位置获取成功',
        message: '已添加当前位置信息',
      }));
    } catch (error) {
      console.error('Failed to get location:', error);
      dispatch(addNotification({
        type: 'error',
        title: '位置获取失败',
        message: '无法获取当前位置',
      }));
    } finally {
      setGettingLocation(false);
    }
  };

  const handleGetWeather = async () => {
    try {
      setGettingWeather(true);
      
      // 模拟天气数据
      const weather: WeatherInfo = {
        temperature: Math.round(Math.random() * 30 + 5),
        humidity: Math.round(Math.random() * 50 + 30),
        condition: ['晴朗', '多云', '阴天', '小雨'][Math.floor(Math.random() * 4)],
        location: '当前位置',
        timestamp: new Date(),
      };

      setFormData(prev => ({ ...prev, weather }));
      
      dispatch(addNotification({
        type: 'success',
        title: '天气获取成功',
        message: '已添加当前天气信息',
      }));
    } catch (error) {
      console.error('Failed to get weather:', error);
      dispatch(addNotification({
        type: 'error',
        title: '天气获取失败',
        message: '无法获取天气信息',
      }));
    } finally {
      setGettingWeather(false);
    }
  };

  const handleSave = async () => {
    if (!formData.title.trim()) {
      dispatch(addNotification({
        type: 'warning',
        title: '请输入标题',
        message: '笔记标题不能为空',
      }));
      return;
    }

    try {
      setSaving(true);
      
      const noteData: Omit<PsychologyNote, 'id'> = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        mood: formData.mood,
        emotion: formData.emotion,
        tags: formData.tags,
        location: formData.location,
        weather: formData.weather,
        userId: profile!.id,
        createdAt: note?.createdAt || new Date(),
        updatedAt: new Date(),
        attachments: note?.attachments || [],
      };

      if (note) {
        await DatabaseService.updateNote(note.id, noteData);
        dispatch(addNotification({
          type: 'success',
          title: '更新成功',
          message: '笔记已更新',
        }));
      } else {
        await DatabaseService.createNote(noteData);
        dispatch(addNotification({
          type: 'success',
          title: '保存成功',
          message: '笔记已保存',
        }));
      }

      onSave();
    } catch (error) {
      console.error('Failed to save note:', error);
      dispatch(addNotification({
        type: 'error',
        title: '保存失败',
        message: '保存笔记时出现错误',
      }));
    } finally {
      setSaving(false);
    }
  };

  const getMoodText = (mood: number) => {
    if (mood >= 8) return '心情很好';
    if (mood >= 6) return '心情不错';
    if (mood >= 4) return '心情一般';
    return '心情不佳';
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 8) return '#4CAF50';
    if (mood >= 6) return '#FFC107';
    if (mood >= 4) return '#FF9800';
    return '#F44336';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6">
          {note ? '编辑笔记' : '新建笔记'}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* 标题 */}
          <TextField
            fullWidth
            label="标题"
            value={formData.title}
            onChange={handleInputChange('title')}
            placeholder="给这篇笔记起个标题..."
          />

          {/* 内容 */}
          <TextField
            fullWidth
            multiline
            rows={8}
            label="内容"
            value={formData.content}
            onChange={handleInputChange('content')}
            placeholder="记录你的心情和想法..."
          />

          {/* 心情指数 */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              心情指数: {formData.mood}/10 - {getMoodText(formData.mood)}
            </Typography>
            <Slider
              value={formData.mood}
              onChange={handleMoodChange}
              min={1}
              max={10}
              step={1}
              marks
              valueLabelDisplay="auto"
              sx={{
                color: getMoodColor(formData.mood),
                '& .MuiSlider-thumb': {
                  bgcolor: getMoodColor(formData.mood),
                },
                '& .MuiSlider-track': {
                  bgcolor: getMoodColor(formData.mood),
                },
              }}
            />
          </Box>

          {/* 情绪标签 */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              情绪标签
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {EMOTION_TAGS.map((tag) => (
                <Chip
                  key={tag.id}
                  label={`${tag.icon} ${tag.name}`}
                  variant={formData.emotion.some(e => e.id === tag.id) ? 'filled' : 'outlined'}
                  onClick={() => handleEmotionToggle(tag)}
                  sx={{
                    bgcolor: formData.emotion.some(e => e.id === tag.id) ? tag.color : 'transparent',
                    color: formData.emotion.some(e => e.id === tag.id) ? 'white' : 'text.primary',
                    borderColor: tag.color,
                    '&:hover': {
                      bgcolor: tag.color,
                      color: 'white',
                    },
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* 标签 */}
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              标签
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
              {formData.tags.map((tag) => (
                <Chip
                  key={tag}
                  label={`#${tag}`}
                  onDelete={() => handleRemoveTag(tag)}
                  size="small"
                />
              ))}
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                size="small"
                placeholder="添加标签..."
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button onClick={handleAddTag} variant="outlined" size="small">
                添加
              </Button>
            </Box>
          </Box>

          {/* 位置和天气 */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Typography variant="subtitle2">位置信息</Typography>
                <Tooltip title="获取当前位置">
                  <IconButton
                    size="small"
                    onClick={handleGetLocation}
                    disabled={gettingLocation}
                  >
                    {gettingLocation ? (
                      <CircularProgress size={16} />
                    ) : (
                      <LocationIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>
              {formData.location && (
                <Typography variant="body2" color="text.secondary">
                  {formData.location.address}
                </Typography>
              )}
            </Box>

            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Typography variant="subtitle2">天气信息</Typography>
                <Tooltip title="获取当前天气">
                  <IconButton
                    size="small"
                    onClick={handleGetWeather}
                    disabled={gettingWeather}
                  >
                    {gettingWeather ? (
                      <CircularProgress size={16} />
                    ) : (
                      <WeatherIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>
              {formData.weather && (
                <Typography variant="body2" color="text.secondary">
                  {formData.weather.condition} {formData.weather.temperature}°C
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} disabled={saving}>
          取消
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={saving}
          startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
          sx={{
            background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
            },
          }}
        >
          {saving ? '保存中...' : '保存'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
