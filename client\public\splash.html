<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LENGSHANG - 启动中</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #FFFFFF 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
            -webkit-app-region: drag;
        }

        .splash-container {
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #87CEEB, #98FB98);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            box-shadow: 0 8px 25px rgba(135, 206, 235, 0.3);
            animation: pulse 2s infinite;
        }

        .app-name {
            font-size: 24px;
            font-weight: 600;
            color: #2F4F4F;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .app-subtitle {
            font-size: 14px;
            color: #708090;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .loading-container {
            position: relative;
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #87CEEB, #98FB98);
            border-radius: 2px;
            animation: loading 2s ease-in-out infinite;
        }

        .loading-text {
            margin-top: 15px;
            font-size: 12px;
            color: #708090;
            opacity: 0.7;
        }

        .version {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #708090;
            opacity: 0.5;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
                margin-left: 0%;
            }
            50% {
                width: 75%;
                margin-left: 12.5%;
            }
            100% {
                width: 0%;
                margin-left: 100%;
            }
        }

        /* 暗色主题适配 */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1E1E2E 0%, #313244 100%);
            }

            .app-name {
                color: #CDD6F4;
            }

            .app-subtitle,
            .loading-text,
            .version {
                color: #A6ADC8;
            }

            .loading-container {
                background: rgba(166, 173, 200, 0.2);
            }

            .loading-bar {
                background: linear-gradient(90deg, #F38BA8, #A6E3A1);
            }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .logo {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .app-name {
                font-size: 20px;
            }

            .loading-container {
                width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">心</div>
        <div class="app-name">LENGSHANG</div>
        <div class="app-subtitle">跨平台心理分析应用</div>
        <div class="loading-container">
            <div class="loading-bar"></div>
        </div>
        <div class="loading-text">正在启动应用...</div>
    </div>
    <div class="version">v1.0.0</div>

    <script>
        // 启动画面逻辑
        const loadingTexts = [
            '正在启动应用...',
            '正在加载组件...',
            '正在初始化数据...',
            '即将完成...'
        ];

        let currentTextIndex = 0;
        const loadingTextElement = document.querySelector('.loading-text');

        // 循环更新加载文本
        const textInterval = setInterval(() => {
            currentTextIndex = (currentTextIndex + 1) % loadingTexts.length;
            loadingTextElement.textContent = loadingTexts[currentTextIndex];
        }, 500);

        // 监听主窗口准备就绪事件
        window.addEventListener('beforeunload', () => {
            clearInterval(textInterval);
        });

        // 添加一些交互效果
        document.querySelector('.logo').addEventListener('click', () => {
            document.querySelector('.logo').style.animation = 'pulse 0.3s ease-in-out';
            setTimeout(() => {
                document.querySelector('.logo').style.animation = 'pulse 2s infinite';
            }, 300);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                // ESC键可以关闭启动画面（开发模式）
                if (window.electronAPI) {
                    window.close();
                }
            }
        });

        // 防止右键菜单
        document.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });

        // 防止选择文本
        document.addEventListener('selectstart', (event) => {
            event.preventDefault();
        });

        // 防止拖拽
        document.addEventListener('dragstart', (event) => {
            event.preventDefault();
        });
    </script>
</body>
</html>
