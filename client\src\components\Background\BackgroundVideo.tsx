import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { motion } from 'framer-motion';

export const BackgroundVideo: React.FC = () => {
  const [videoSrc, setVideoSrc] = useState<string>('');
  const [fallbackImage, setFallbackImage] = useState<string>('');

  useEffect(() => {
    // 获取背景视频或图片
    fetchBackgroundMedia();
  }, []);

  const fetchBackgroundMedia = async () => {
    try {
      // 尝试获取视频
      const videoResponse = await fetch('https://api.hn/video.php?return=json');
      if (videoResponse.ok) {
        const videoData = await videoResponse.json();
        if (videoData && videoData.url) {
          setVideoSrc(videoData.url);
          return;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch background video:', error);
    }

    try {
      // 如果视频获取失败，使用必应壁纸作为背景
      const imageResponse = await fetch('https://api.cenguigui.cn/api/bing/api.php?type=json');
      if (imageResponse.ok) {
        const imageData = await imageResponse.json();
        if (imageData && imageData.url) {
          setFallbackImage(imageData.url);
        }
      }
    } catch (error) {
      console.warn('Failed to fetch background image:', error);
      // 使用默认渐变背景
      setFallbackImage('');
    }
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: -1,
        overflow: 'hidden',
      }}
    >
      {/* 视频背景 */}
      {videoSrc && (
        <motion.video
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          autoPlay
          muted
          loop
          playsInline
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            minWidth: '100%',
            minHeight: '100%',
            width: 'auto',
            height: 'auto',
            transform: 'translate(-50%, -50%)',
            objectFit: 'cover',
          }}
          onError={() => {
            console.warn('Video failed to load, falling back to image');
            setVideoSrc('');
          }}
        >
          <source src={videoSrc} type="video/mp4" />
        </motion.video>
      )}

      {/* 图片背景（视频加载失败时的备选） */}
      {!videoSrc && fallbackImage && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundImage: `url(${fallbackImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        />
      )}

      {/* 默认渐变背景 */}
      {!videoSrc && !fallbackImage && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 50%, #FFB6C1 100%)',
          }}
        />
      )}

      {/* 覆盖层 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'rgba(0, 0, 0, 0.3)',
          backdropFilter: 'blur(1px)',
        }}
      />

      {/* 动态粒子效果 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
        }}
      >
        {/* 浮动圆圈 */}
        {[...Array(6)].map((_, index) => (
          <motion.div
            key={index}
            style={{
              position: 'absolute',
              width: Math.random() * 100 + 50,
              height: Math.random() * 100 + 50,
              borderRadius: '50%',
              background: `rgba(255, 255, 255, ${Math.random() * 0.1 + 0.05})`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.random() * 20 - 10, 0],
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: Math.random() * 5,
            }}
          />
        ))}

        {/* 光晕效果 */}
        <motion.div
          style={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: 200,
            height: 200,
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(135, 206, 235, 0.2) 0%, transparent 70%)',
            filter: 'blur(20px)',
          }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        <motion.div
          style={{
            position: 'absolute',
            bottom: '30%',
            left: '15%',
            width: 150,
            height: 150,
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(152, 251, 152, 0.2) 0%, transparent 70%)',
            filter: 'blur(15px)',
          }}
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </Box>
    </Box>
  );
};
