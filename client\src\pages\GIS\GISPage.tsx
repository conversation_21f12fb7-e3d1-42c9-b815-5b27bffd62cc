import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Paper,
  Chip,
  ToggleButton,
  ToggleButtonGroup,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
} from '@mui/material';
import {
  Map as MapIcon,
  LocationOn as LocationIcon,
  TrendingUp as TrendIcon,
  Layers as LayersIcon,
  FilterList as FilterIcon,
  MyLocation as MyLocationIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';

import { RootState } from '../../store';
import { EMOTION_TAGS } from '../../shared/constants';

// 模拟地图组件（实际应用中应该使用Leaflet或其他地图库）
const MapContainer: React.FC<{ 
  center: { lat: number; lng: number };
  zoom: number;
  style: string;
  showHeatmap: boolean;
  showEmotionPoints: boolean;
}> = ({ center, zoom, style, showHeatmap, showEmotionPoints }) => {
  return (
    <Box
      sx={{
        width: '100%',
        height: 400,
        bgcolor: 'grey.100',
        borderRadius: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* 地图背景 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: style === 'satellite' 
            ? 'linear-gradient(45deg, #2E7D32, #388E3C, #4CAF50)'
            : style === 'dark'
            ? 'linear-gradient(45deg, #212121, #424242, #616161)'
            : 'linear-gradient(45deg, #E3F2FD, #BBDEFB, #90CAF9)',
          opacity: 0.8,
        }}
      />
      
      {/* 地图内容 */}
      <Box sx={{ position: 'relative', textAlign: 'center', color: 'white' }}>
        <MapIcon sx={{ fontSize: 64, mb: 2 }} />
        <Typography variant="h6">
          交互式地图
        </Typography>
        <Typography variant="body2">
          中心: {center.lat.toFixed(4)}, {center.lng.toFixed(4)}
        </Typography>
        <Typography variant="body2">
          缩放: {zoom}x
        </Typography>
        
        {/* 模拟情绪点 */}
        {showEmotionPoints && (
          <Box sx={{ position: 'absolute', top: 20, left: 20 }}>
            <LocationIcon sx={{ color: '#F44336', fontSize: 20 }} />
          </Box>
        )}
        
        {/* 模拟热力图 */}
        {showHeatmap && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 20,
              right: 20,
              width: 60,
              height: 60,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(244,67,54,0.8) 0%, rgba(244,67,54,0.2) 70%, transparent 100%)',
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export const GISPage: React.FC = () => {
  const dispatch = useDispatch();
  const { profile } = useSelector((state: RootState) => state.user);
  
  const [mapSettings, setMapSettings] = useState({
    center: { lat: 39.9042, lng: 116.4074 }, // 北京
    zoom: 10,
    style: 'default',
    showHeatmap: true,
    showEmotionPoints: true,
    showTrajectory: false,
  });
  
  const [filters, setFilters] = useState({
    emotions: [] as string[],
    moodRange: [1, 10] as [number, number],
    timeRange: 7, // 天数
  });
  
  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);

  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    if ('geolocation' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        setLocationPermission(permission.state);
        
        if (permission.state === 'granted') {
          getCurrentLocation();
        }
      } catch (error) {
        console.error('Error checking location permission:', error);
      }
    }
  };

  const getCurrentLocation = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
          setMapSettings(prev => ({
            ...prev,
            center: { lat: latitude, lng: longitude },
          }));
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  const handleRequestLocation = async () => {
    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      if (permission.state === 'prompt') {
        getCurrentLocation();
      } else if (permission.state === 'denied') {
        alert('位置权限被拒绝，请在浏览器设置中允许位置访问。');
      }
    } catch (error) {
      getCurrentLocation(); // 直接尝试获取位置
    }
  };

  const handleEmotionFilterChange = (emotionId: string) => {
    setFilters(prev => ({
      ...prev,
      emotions: prev.emotions.includes(emotionId)
        ? prev.emotions.filter(id => id !== emotionId)
        : [...prev.emotions, emotionId],
    }));
  };

  const handleMapStyleChange = (style: string) => {
    setMapSettings(prev => ({ ...prev, style }));
  };

  const handleLayerToggle = (layer: string, value: boolean) => {
    setMapSettings(prev => ({ ...prev, [layer]: value }));
  };

  // 模拟数据
  const emotionHotspots = [
    { location: '公园', emotion: '快乐', frequency: 85, color: '#4CAF50' },
    { location: '办公室', emotion: '压力', frequency: 72, color: '#F44336' },
    { location: '家', emotion: '平静', frequency: 90, color: '#2196F3' },
    { location: '咖啡厅', emotion: '放松', frequency: 68, color: '#FF9800' },
  ];

  const moodPatterns = [
    { time: '早晨', averageMood: 7.2, color: '#FFC107' },
    { time: '中午', averageMood: 6.8, color: '#FF9800' },
    { time: '下午', averageMood: 6.5, color: '#FF5722' },
    { time: '晚上', averageMood: 7.8, color: '#9C27B0' },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            GIS 地理分析
          </Typography>
          <Typography variant="body1" color="text.secondary">
            基于地理位置的情绪数据分析与可视化
          </Typography>
        </Box>
      </motion.div>

      {/* 位置权限提示 */}
      {locationPermission !== 'granted' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <Alert 
            severity="info" 
            action={
              <Button color="inherit" size="small" onClick={handleRequestLocation}>
                允许位置访问
              </Button>
            }
            sx={{ mb: 3 }}
          >
            启用位置服务以获得更好的地理分析体验
          </Alert>
        </motion.div>
      )}

      <Grid container spacing={3}>
        {/* 地图区域 */}
        <Grid item xs={12} lg={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <Paper sx={{ p: 2 }}>
              {/* 地图控制栏 */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">情绪地图</Typography>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {currentLocation && (
                    <Button
                      size="small"
                      startIcon={<MyLocationIcon />}
                      onClick={() => setMapSettings(prev => ({ ...prev, center: currentLocation }))}
                    >
                      我的位置
                    </Button>
                  )}
                  
                  <ToggleButtonGroup
                    size="small"
                    value={mapSettings.style}
                    exclusive
                    onChange={(e, value) => value && handleMapStyleChange(value)}
                  >
                    <ToggleButton value="default">默认</ToggleButton>
                    <ToggleButton value="satellite">卫星</ToggleButton>
                    <ToggleButton value="dark">暗色</ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Box>

              {/* 地图组件 */}
              <MapContainer
                center={mapSettings.center}
                zoom={mapSettings.zoom}
                style={mapSettings.style}
                showHeatmap={mapSettings.showHeatmap}
                showEmotionPoints={mapSettings.showEmotionPoints}
              />

              {/* 图层控制 */}
              <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label="情绪热力图"
                  variant={mapSettings.showHeatmap ? 'filled' : 'outlined'}
                  onClick={() => handleLayerToggle('showHeatmap', !mapSettings.showHeatmap)}
                  color="primary"
                />
                <Chip
                  label="情绪点位"
                  variant={mapSettings.showEmotionPoints ? 'filled' : 'outlined'}
                  onClick={() => handleLayerToggle('showEmotionPoints', !mapSettings.showEmotionPoints)}
                  color="secondary"
                />
                <Chip
                  label="活动轨迹"
                  variant={mapSettings.showTrajectory ? 'filled' : 'outlined'}
                  onClick={() => handleLayerToggle('showTrajectory', !mapSettings.showTrajectory)}
                  color="success"
                />
              </Box>
            </Paper>
          </motion.div>
        </Grid>

        {/* 控制面板 */}
        <Grid item xs={12} lg={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* 情绪筛选 */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <FilterIcon sx={{ mr: 1 }} />
                  筛选条件
                </Typography>
                
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  情绪类型
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {EMOTION_TAGS.slice(0, 6).map((tag) => (
                    <Chip
                      key={tag.id}
                      label={tag.name}
                      size="small"
                      variant={filters.emotions.includes(tag.id) ? 'filled' : 'outlined'}
                      onClick={() => handleEmotionFilterChange(tag.id)}
                      sx={{
                        bgcolor: filters.emotions.includes(tag.id) ? tag.color : 'transparent',
                        color: filters.emotions.includes(tag.id) ? 'white' : 'text.primary',
                        borderColor: tag.color,
                      }}
                    />
                  ))}
                </Box>
                
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  心情范围: {filters.moodRange[0]} - {filters.moodRange[1]}
                </Typography>
                <Slider
                  value={filters.moodRange}
                  onChange={(e, value) => setFilters(prev => ({ ...prev, moodRange: value as [number, number] }))}
                  valueLabelDisplay="auto"
                  min={1}
                  max={10}
                  sx={{ mb: 2 }}
                />
                
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  时间范围
                </Typography>
                <FormControl fullWidth size="small">
                  <Select
                    value={filters.timeRange}
                    onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value as number }))}
                  >
                    <MenuItem value={7}>最近7天</MenuItem>
                    <MenuItem value={30}>最近30天</MenuItem>
                    <MenuItem value={90}>最近3个月</MenuItem>
                    <MenuItem value={365}>最近1年</MenuItem>
                  </Select>
                </FormControl>
              </Paper>

              {/* 情绪热点 */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  情绪热点
                </Typography>
                {emotionHotspots.map((hotspot, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {hotspot.location}
                      </Typography>
                      <Chip
                        label={hotspot.emotion}
                        size="small"
                        sx={{ bgcolor: hotspot.color, color: 'white' }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ flex: 1, bgcolor: 'grey.200', borderRadius: 1, height: 6 }}>
                        <Box
                          sx={{
                            width: `${hotspot.frequency}%`,
                            height: '100%',
                            bgcolor: hotspot.color,
                            borderRadius: 1,
                          }}
                        />
                      </Box>
                      <Typography variant="caption">
                        {hotspot.frequency}%
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Paper>

              {/* 时间模式 */}
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  心情时间模式
                </Typography>
                {moodPatterns.map((pattern, index) => (
                  <Box key={index} sx={{ mb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">
                        {pattern.time}
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {pattern.averageMood.toFixed(1)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: '100%', bgcolor: 'grey.200', borderRadius: 1, height: 4, mt: 0.5 }}>
                      <Box
                        sx={{
                          width: `${(pattern.averageMood / 10) * 100}%`,
                          height: '100%',
                          bgcolor: pattern.color,
                          borderRadius: 1,
                        }}
                      />
                    </Box>
                  </Box>
                ))}
              </Paper>
            </Box>
          </motion.div>
        </Grid>
      </Grid>

      {/* 使用说明 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <Box
          sx={{
            mt: 4,
            p: 3,
            borderRadius: 2,
            background: 'linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(76, 175, 80, 0.1))',
            border: '1px solid rgba(33, 150, 243, 0.3)',
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
            🗺️ 地理分析功能
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>情绪热力图</strong>：显示不同区域的情绪密度分布
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>情绪点位</strong>：标记具体的情绪记录位置
              </Typography>
              <Typography variant="body2">
                • <strong>活动轨迹</strong>：显示您的移动路径和情绪变化
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>热点分析</strong>：识别情绪高发区域和模式
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                • <strong>时间模式</strong>：分析不同时段的心情变化
              </Typography>
              <Typography variant="body2">
                • <strong>个性化建议</strong>：基于地理数据提供生活建议
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </motion.div>
    </Container>
  );
};
