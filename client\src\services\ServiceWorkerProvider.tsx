import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { addNotification } from '../store/slices/appSlice';

interface ServiceWorkerContextType {
  isOnline: boolean;
  isUpdateAvailable: boolean;
  updateServiceWorker: () => void;
  registration: ServiceWorkerRegistration | null;
}

const ServiceWorkerContext = createContext<ServiceWorkerContextType | undefined>(undefined);

export const ServiceWorkerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useDispatch();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);

  useEffect(() => {
    // 监听网络状态变化
    const handleOnline = () => {
      setIsOnline(true);
      dispatch(addNotification({
        type: 'success',
        title: '网络连接已恢复',
        message: '您现在可以正常使用所有功能',
      }));
    };

    const handleOffline = () => {
      setIsOnline(false);
      dispatch(addNotification({
        type: 'warning',
        title: '网络连接已断开',
        message: '应用将在离线模式下运行',
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 注册Service Worker
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      registerServiceWorker();
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [dispatch]);

  const registerServiceWorker = async () => {
    try {
      const reg = await navigator.serviceWorker.register('/sw.js');
      setRegistration(reg);

      // 监听Service Worker更新
      reg.addEventListener('updatefound', () => {
        const newWorker = reg.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setWaitingWorker(newWorker);
              setIsUpdateAvailable(true);
              
              dispatch(addNotification({
                type: 'info',
                title: '应用更新可用',
                message: '点击刷新以获取最新版本',
                actions: [
                  {
                    label: '立即更新',
                    action: 'update-app',
                  },
                ],
              }));
            }
          });
        }
      });

      // 监听Service Worker控制变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload();
      });

      console.log('Service Worker registered successfully');
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const updateServiceWorker = () => {
    if (waitingWorker) {
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
      setIsUpdateAvailable(false);
    }
  };

  const contextValue: ServiceWorkerContextType = {
    isOnline,
    isUpdateAvailable,
    updateServiceWorker,
    registration,
  };

  return (
    <ServiceWorkerContext.Provider value={contextValue}>
      {children}
    </ServiceWorkerContext.Provider>
  );
};

export const useServiceWorker = () => {
  const context = useContext(ServiceWorkerContext);
  if (context === undefined) {
    throw new Error('useServiceWorker must be used within a ServiceWorkerProvider');
  }
  return context;
};
