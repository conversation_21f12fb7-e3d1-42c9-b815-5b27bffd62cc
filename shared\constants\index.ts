// 应用常量定义

// 应用信息
export const APP_INFO = {
  NAME: 'LENGSHANG',
  VERSION: '1.0.0',
  DESCRIPTION: '跨平台心理分析应用',
  AUTHOR: 'LENGSHANG Team',
  WEBSITE: 'https://lengshang.app',
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

// 支持的语言
export const SUPPORTED_LANGUAGES = {
  'zh-CN': '简体中文',
  'en-US': 'English',
  'ja-JP': '日本語',
} as const;

// 主题配色
export const THEMES = {
  light: {
    primary: '#87CEEB', // 天空蓝
    primaryGradient: 'linear-gradient(135deg, #87CEEB 0%, #FFFFFF 100%)',
    secondary: '#FFB6C1', // 樱花粉
    accent: '#98FB98', // 薄荷绿
    text: '#2F4F4F', // 深灰蓝
    background: '#FFFFFF',
    surface: '#F8F9FA',
    border: '#E9ECEF',
  },
  dark: {
    primary: '#1E1E2E', // 深紫灰
    primaryGradient: 'linear-gradient(135deg, #1E1E2E 0%, #313244 100%)',
    secondary: '#F38BA8', // 玫瑰金
    accent: '#A6E3A1', // 薄荷绿
    text: '#CDD6F4', // 淡紫白
    background: '#11111B',
    surface: '#1E1E2E',
    border: '#313244',
  },
} as const;

// 情绪标签
export const EMOTION_TAGS = [
  { id: 'happy', name: '快乐', color: '#FFD700', icon: '😊' },
  { id: 'sad', name: '悲伤', color: '#4169E1', icon: '😢' },
  { id: 'angry', name: '愤怒', color: '#FF6347', icon: '😠' },
  { id: 'anxious', name: '焦虑', color: '#FF8C00', icon: '😰' },
  { id: 'calm', name: '平静', color: '#98FB98', icon: '😌' },
  { id: 'excited', name: '兴奋', color: '#FF69B4', icon: '🤩' },
  { id: 'tired', name: '疲惫', color: '#708090', icon: '😴' },
  { id: 'confused', name: '困惑', color: '#DDA0DD', icon: '😕' },
  { id: 'grateful', name: '感激', color: '#F0E68C', icon: '🙏' },
  { id: 'lonely', name: '孤独', color: '#696969', icon: '😔' },
  { id: 'hopeful', name: '希望', color: '#87CEEB', icon: '🌟' },
  { id: 'stressed', name: '压力', color: '#CD5C5C', icon: '😫' },
] as const;

// 心理测试类型
export const TEST_TYPES = {
  MBTI: {
    name: 'MBTI人格测试',
    description: '迈尔斯-布里格斯类型指标',
    duration: 15,
    questions: 93,
  },
  BIG_FIVE: {
    name: '大五人格测试',
    description: 'NEO-PI简化版',
    duration: 10,
    questions: 44,
  },
  HOLLAND: {
    name: '霍兰德职业兴趣测试',
    description: '职业兴趣评估',
    duration: 12,
    questions: 60,
  },
  SDS: {
    name: '抑郁自评量表',
    description: 'SDS-20题',
    duration: 5,
    questions: 20,
  },
  SAS: {
    name: '焦虑自评量表',
    description: 'SAS-20题',
    duration: 5,
    questions: 20,
  },
  PSS: {
    name: '压力测试',
    description: 'PSS-14题',
    duration: 5,
    questions: 14,
  },
} as const;

// VR场景类型
export const VR_SCENE_TYPES = {
  meditation: {
    name: '冥想花园',
    description: '虚拟禅意花园，配合呼吸引导',
    icon: '🧘',
    color: '#98FB98',
  },
  emotion_release: {
    name: '情绪释放空间',
    description: '可破坏的虚拟物体，释放负面情绪',
    icon: '💥',
    color: '#FF6347',
  },
  memory_palace: {
    name: '记忆宫殿',
    description: '3D空间记忆训练',
    icon: '🏛️',
    color: '#DDA0DD',
  },
  social_simulation: {
    name: '社交模拟',
    description: '虚拟社交场景练习',
    icon: '👥',
    color: '#87CEEB',
  },
  fear_exposure: {
    name: '恐惧暴露',
    description: '渐进式恐惧症治疗场景',
    icon: '🦋',
    color: '#FFB6C1',
  },
} as const;

// API端点
export const API_ENDPOINTS = {
  // 图片资源
  BING_WALLPAPER: 'https://api.cenguigui.cn/api/bing/api.php?type=json',
  ANIME_IMAGES: 'https://api.cenguigui.cn/api/pic/',
  
  // 音乐服务
  RANDOM_MUSIC: 'https://api.uomg.com/api/rand.music?sort=热歌榜&format=json',
  KUWO_SEARCH: 'https://api.cenguigui.cn/api/music/kuwo/KoWo_Dg.php',
  QQ_MUSIC: 'https://zj.v.api.aa1.cn/api/qqmusic/demo.php',
  
  // 内容服务
  DAILY_POETRY: 'https://api.cenguigui.cn/api/yyan/api.php',
  HISTORY_TODAY: 'https://v2.api-m.com/api/history',
  DAILY_NEWS: 'https://www.hhlqilongzhu.cn/api/60s.php',
  
  // 视频内容
  RANDOM_VIDEOS: [
    'https://api.hn/video.php?return=json',
    'https://api.heylie.cn/api/video?v=xq'
  ],
} as const;

// 本地存储键
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'lengshang_user_preferences',
  THEME: 'lengshang_theme',
  LANGUAGE: 'lengshang_language',
  NOTES_CACHE: 'lengshang_notes_cache',
  TEST_PROGRESS: 'lengshang_test_progress',
  VR_SETTINGS: 'lengshang_vr_settings',
  LAST_SYNC: 'lengshang_last_sync',
} as const;

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // 数据错误
  NOT_FOUND: 'NOT_FOUND',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  DATA_CORRUPTION: 'DATA_CORRUPTION',
  
  // 功能错误
  VR_NOT_SUPPORTED: 'VR_NOT_SUPPORTED',
  GEOLOCATION_DENIED: 'GEOLOCATION_DENIED',
  MICROPHONE_DENIED: 'MICROPHONE_DENIED',
  
  // 系统错误
  STORAGE_FULL: 'STORAGE_FULL',
  MEMORY_LIMIT: 'MEMORY_LIMIT',
  PERFORMANCE_ISSUE: 'PERFORMANCE_ISSUE',
} as const;

// 性能基准
export const PERFORMANCE_BENCHMARKS = {
  COLD_START_TIME: 1500, // 毫秒
  HOT_START_TIME: 500, // 毫秒
  PAGE_TRANSITION: 300, // 毫秒
  MEMORY_LIMIT_IDLE: 300 * 1024 * 1024, // 300MB
  MEMORY_LIMIT_PEAK: 800 * 1024 * 1024, // 800MB
  CPU_IDLE_LIMIT: 5, // 百分比
  CPU_LOAD_LIMIT: 30, // 百分比
  TARGET_FPS: 60,
  API_TIMEOUT: 2000, // 毫秒
  OFFLINE_SUPPORT_HOURS: 72,
} as const;

// 文件类型限制
export const FILE_CONSTRAINTS = {
  IMAGE: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },
  AUDIO: {
    MAX_SIZE: 50 * 1024 * 1024, // 50MB
    ALLOWED_TYPES: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'],
  },
  VIDEO: {
    MAX_SIZE: 100 * 1024 * 1024, // 100MB
    ALLOWED_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  },
  DOCUMENT: {
    MAX_SIZE: 20 * 1024 * 1024, // 20MB
    ALLOWED_TYPES: ['application/pdf', 'text/plain', 'text/markdown'],
  },
} as const;

// 动画配置
export const ANIMATION_CONFIG = {
  PAGE_TRANSITIONS: {
    duration: 600,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    effects: ['fade', 'slide', 'scale'],
  },
  MICRO_INTERACTIONS: {
    button_hover: { scale: 1.05, duration: 200 },
    card_elevation: { shadow: '0 8px 25px rgba(0,0,0,0.15)' },
    loading_states: 'skeleton + shimmer',
  },
  DATA_VISUALIZATION: {
    chart_animations: true,
    progressive_loading: true,
    interactive_tooltips: true,
  },
} as const;

// 默认设置
export const DEFAULT_SETTINGS = {
  language: 'zh-CN' as const,
  theme: 'light' as const,
  animations: true,
  notifications: true,
  autoStart: false,
  cloudSync: false,
  dataRetention: 365, // 天
  backupFrequency: 'weekly' as const,
} as const;
