import React, { useEffect, useState } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface PoetryData {
  content: string;
  author: string;
  dynasty?: string;
  title?: string;
}

export const PoetryWidget: React.FC = () => {
  const [poetry, setPoetry] = useState<PoetryData | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchPoetry();
  }, []);

  const fetchPoetry = async () => {
    try {
      setLoading(true);
      
      // 尝试从API获取诗词
      const response = await fetch('https://api.cenguigui.cn/api/yyan/api.php');
      if (response.ok) {
        const text = await response.text();
        // 解析返回的诗词数据
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length >= 2) {
          setPoetry({
            content: lines[0],
            author: lines[1],
          });
          return;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch poetry from API:', error);
    }

    // 如果API失败，使用本地诗词库
    const localPoetry = getRandomLocalPoetry();
    setPoetry(localPoetry);
    setLoading(false);
  };

  const getRandomLocalPoetry = (): PoetryData => {
    const poetryList = [
      {
        content: "山重水复疑无路，柳暗花明又一村。",
        author: "陆游",
        dynasty: "宋",
        title: "游山西村"
      },
      {
        content: "海内存知己，天涯若比邻。",
        author: "王勃",
        dynasty: "唐",
        title: "送杜少府之任蜀州"
      },
      {
        content: "落红不是无情物，化作春泥更护花。",
        author: "龚自珍",
        dynasty: "清",
        title: "己亥杂诗"
      },
      {
        content: "长风破浪会有时，直挂云帆济沧海。",
        author: "李白",
        dynasty: "唐",
        title: "行路难"
      },
      {
        content: "会当凌绝顶，一览众山小。",
        author: "杜甫",
        dynasty: "唐",
        title: "望岳"
      },
      {
        content: "千磨万击还坚劲，任尔东西南北风。",
        author: "郑燮",
        dynasty: "清",
        title: "竹石"
      },
      {
        content: "不畏浮云遮望眼，只缘身在最高层。",
        author: "王安石",
        dynasty: "宋",
        title: "登飞来峰"
      },
      {
        content: "宝剑锋从磨砺出，梅花香自苦寒来。",
        author: "佚名",
        dynasty: "",
        title: "警世贤文"
      },
      {
        content: "纸上得来终觉浅，绝知此事要躬行。",
        author: "陆游",
        dynasty: "宋",
        title: "冬夜读书示子聿"
      },
      {
        content: "问渠那得清如许，为有源头活水来。",
        author: "朱熹",
        dynasty: "宋",
        title: "观书有感"
      }
    ];

    return poetryList[Math.floor(Math.random() * poetryList.length)];
  };

  const handleRefresh = () => {
    fetchPoetry();
  };

  if (!poetry) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
    >
      <Box
        sx={{
          textAlign: 'center',
          mb: 3,
          position: 'relative',
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={poetry.content}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.6 }}
          >
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255,255,255,0.95)',
                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                fontFamily: '"KaiTi", "楷体", serif',
                lineHeight: 1.6,
                mb: 1,
                px: 2,
              }}
            >
              {poetry.content}
            </Typography>
            
            <Typography
              variant="body2"
              sx={{
                color: 'rgba(255,255,255,0.8)',
                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                fontStyle: 'italic',
              }}
            >
              —— {poetry.dynasty && `${poetry.dynasty}·`}{poetry.author}
              {poetry.title && ` 《${poetry.title}》`}
            </Typography>
          </motion.div>
        </AnimatePresence>

        {/* 刷新按钮 */}
        <motion.div
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
          }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <IconButton
            onClick={handleRefresh}
            disabled={loading}
            sx={{
              color: 'rgba(255,255,255,0.7)',
              '&:hover': {
                color: 'rgba(255,255,255,0.9)',
                bgcolor: 'rgba(255,255,255,0.1)',
              },
            }}
            size="small"
          >
            <motion.div
              animate={loading ? { rotate: 360 } : {}}
              transition={loading ? { duration: 1, repeat: Infinity, ease: 'linear' } : {}}
            >
              <RefreshIcon fontSize="small" />
            </motion.div>
          </IconButton>
        </motion.div>

        {/* 装饰性引号 */}
        <Box
          sx={{
            position: 'absolute',
            top: -10,
            left: 10,
            fontSize: '2rem',
            color: 'rgba(255,255,255,0.3)',
            fontFamily: 'serif',
            lineHeight: 1,
          }}
        >
          "
        </Box>
        
        <Box
          sx={{
            position: 'absolute',
            bottom: -10,
            right: 10,
            fontSize: '2rem',
            color: 'rgba(255,255,255,0.3)',
            fontFamily: 'serif',
            lineHeight: 1,
            transform: 'rotate(180deg)',
          }}
        >
          "
        </Box>
      </Box>
    </motion.div>
  );
};
