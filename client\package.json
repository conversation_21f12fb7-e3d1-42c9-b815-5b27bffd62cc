{"name": "lengshang-client", "version": "1.0.0", "description": "LENGSHANG 心理分析应用客户端", "main": "public/electron.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && npm run electron:dev\"", "dev:react": "react-scripts start", "dev:tauri": "tauri dev", "build": "react-scripts build", "build:electron": "npm run build && electron-builder", "build:tauri": "tauri build", "test": "react-scripts test", "eject": "react-scripts eject", "electron:dev": "cross-env ELECTRON_IS_DEV=true electron .", "electron:build": "electron-builder", "electron:dist": "npm run build && npm run electron:build", "tauri:build": "tauri build", "tauri:dev": "tauri dev", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.1", "@mui/icons-material": "^5.14.1", "@reduxjs/toolkit": "^1.9.5", "@tauri-apps/api": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "framer-motion": "^10.12.18", "lottie-react": "^2.4.0", "three": "^0.154.0", "@react-three/fiber": "^8.13.5", "@react-three/drei": "^9.79.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "d3": "^7.8.5", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "recharts": "^2.7.2", "victory": "^36.6.8", "tone": "^14.7.77", "video.js": "^8.3.0", "hls.js": "^1.4.9", "@monaco-editor/react": "^4.5.1", "react-markdown": "^8.0.7", "remark-gfm": "^3.0.1", "rehype-katex": "^6.0.3", "remark-math": "^5.1.1", "katex": "^0.16.8", "i18next": "^23.2.11", "react-i18next": "^13.0.2", "i18next-browser-languagedetector": "^7.1.0", "date-fns": "^2.30.0", "axios": "^1.4.0", "lodash": "^4.17.21", "uuid": "^9.0.0", "crypto-js": "^4.1.1", "dexie": "^3.2.4", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/node": "^20.4.2", "@types/three": "^0.154.0", "@types/d3": "^7.4.0", "@types/leaflet": "^1.9.3", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@types/crypto-js": "^4.1.1", "@tauri-apps/cli": "^1.4.0", "electron": "^25.3.0", "electron-builder": "^24.6.3", "electron-is-dev": "^2.0.0", "concurrently": "^8.2.0", "wait-on": "^7.0.1", "cross-env": "^7.0.3", "typescript": "^5.1.6", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.26"}, "build": {"appId": "com.lengshang.psychology", "productName": "LENGSHANG Psychology", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.healthcare-fitness", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "pkg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "msi", "arch": ["x64", "arm64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}]}, "protocols": [{"name": "lengshang-protocol", "schemes": ["lengshangapp"]}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}