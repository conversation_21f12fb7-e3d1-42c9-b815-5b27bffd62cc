{"name": "lengshang-client", "version": "1.0.0", "description": "LENGSHANG 心理分析应用客户端", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && npm run electron:dev\"", "dev:react": "react-scripts start", "dev:tauri": "tauri dev", "build": "react-scripts build", "build:electron": "npm run build && electron-builder", "build:tauri": "tauri build", "test": "react-scripts test", "eject": "react-scripts eject", "electron:dev": "cross-env ELECTRON_IS_DEV=true electron .", "electron:build": "electron-builder", "electron:dist": "npm run build && npm run electron:build", "tauri:build": "tauri build", "tauri:dev": "tauri dev", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@monaco-editor/react": "^4.5.1", "@mui/icons-material": "^5.14.1", "@mui/material": "^5.14.1", "@react-three/drei": "^9.79.0", "@react-three/fiber": "^8.13.5", "@reduxjs/toolkit": "^1.9.5", "@tauri-apps/api": "^1.4.0", "axios": "^1.4.0", "crypto-js": "^4.1.1", "d3": "^7.8.5", "date-fns": "^2.30.0", "dexie": "^3.2.4", "echarts": "^5.4.2", "echarts-for-react": "^3.0.2", "framer-motion": "^10.12.18", "hls.js": "^1.4.9", "i18next": "^23.2.11", "i18next-browser-languagedetector": "^7.1.0", "katex": "^0.16.8", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-i18next": "^13.0.2", "react-leaflet": "^4.2.1", "react-markdown": "^8.0.7", "react-redux": "^8.1.1", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "recharts": "^2.7.2", "redux-persist": "^6.0.0", "rehype-katex": "^6.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "three": "^0.154.0", "tone": "^14.7.77", "uuid": "^9.0.0", "victory": "^36.6.8", "video.js": "^8.3.0", "workbox-background-sync": "^7.0.0", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0"}, "devDependencies": {"@tauri-apps/cli": "^1.4.0", "@types/crypto-js": "^4.1.1", "@types/d3": "^7.4.0", "@types/leaflet": "^1.9.3", "@types/lodash": "^4.14.195", "@types/node": "^20.4.2", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/three": "^0.154.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.14", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^25.3.0", "electron-builder": "^24.6.3", "electron-is-dev": "^2.0.0", "postcss": "^8.4.26", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "wait-on": "^7.0.1"}, "build": {"appId": "com.lengshang.psychology", "productName": "LENGSHANG Psychology", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.healthcare-fitness", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "pkg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "msi", "arch": ["x64", "arm64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}]}, "protocols": [{"name": "lengshang-protocol", "schemes": ["lengshangapp"]}]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}