import React, { useEffect, useState } from 'react';
import { Card, CardContent, Typography, Box, CircularProgress } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Wb<PERSON><PERSON>ny as SunnyIcon,
  Cloud as CloudyIcon,
  Grain as RainIcon,
  AcUnit as SnowIcon,
  Visibility as FogIcon,
} from '@mui/icons-material';

interface WeatherData {
  temperature: number;
  humidity: number;
  condition: string;
  location: string;
  icon: string;
}

export const WeatherWidget: React.FC = () => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWeatherData();
  }, []);

  const fetchWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取用户位置
      const position = await getCurrentPosition();
      const { latitude, longitude } = position.coords;

      // 模拟天气数据（实际应用中应该调用真实的天气API）
      const mockWeatherData: WeatherData = {
        temperature: Math.round(Math.random() * 30 + 5), // 5-35度
        humidity: Math.round(Math.random() * 50 + 30), // 30-80%
        condition: getRandomWeatherCondition(),
        location: '当前位置',
        icon: 'sunny',
      };

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWeather(mockWeatherData);
    } catch (error) {
      console.error('Failed to fetch weather data:', error);
      setError('无法获取天气信息');
      
      // 使用默认天气数据
      setWeather({
        temperature: 22,
        humidity: 65,
        condition: '晴朗',
        location: '北京',
        icon: 'sunny',
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentPosition = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        resolve,
        reject,
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5分钟缓存
        }
      );
    });
  };

  const getRandomWeatherCondition = () => {
    const conditions = ['晴朗', '多云', '阴天', '小雨', '大雨', '雪', '雾'];
    return conditions[Math.floor(Math.random() * conditions.length)];
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case '晴朗':
        return <SunnyIcon sx={{ fontSize: 40, color: '#FFA500' }} />;
      case '多云':
      case '阴天':
        return <CloudyIcon sx={{ fontSize: 40, color: '#87CEEB' }} />;
      case '小雨':
      case '大雨':
        return <RainIcon sx={{ fontSize: 40, color: '#4169E1' }} />;
      case '雪':
        return <SnowIcon sx={{ fontSize: 40, color: '#E0E0E0' }} />;
      case '雾':
        return <FogIcon sx={{ fontSize: 40, color: '#D3D3D3' }} />;
      default:
        return <SunnyIcon sx={{ fontSize: 40, color: '#FFA500' }} />;
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 0) return '#87CEEB'; // 冷蓝色
    if (temp < 10) return '#98FB98'; // 绿色
    if (temp < 25) return '#FFD700'; // 金色
    if (temp < 35) return '#FFA500'; // 橙色
    return '#FF6347'; // 红色
  };

  if (loading) {
    return (
      <Card
        sx={{
          height: 200,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        }}
      >
        <CardContent
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}
        >
          <CircularProgress sx={{ color: 'white' }} />
        </CardContent>
      </Card>
    );
  }

  if (error || !weather) {
    return (
      <Card
        sx={{
          height: 200,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        }}
      >
        <CardContent
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center',
          }}
        >
          <Typography color="rgba(255,255,255,0.8)">
            {error || '天气信息不可用'}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card
        sx={{
          height: 200,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            background: 'rgba(255, 255, 255, 0.15)',
          },
        }}
        onClick={fetchWeatherData}
      >
        <CardContent sx={{ height: '100%', p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography
              variant="h6"
              sx={{ color: 'white', fontWeight: 'bold', flex: 1 }}
            >
              天气
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: 'rgba(255,255,255,0.8)' }}
            >
              {weather.location}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            >
              {getWeatherIcon(weather.condition)}
            </motion.div>
            
            <Box sx={{ ml: 2, flex: 1 }}>
              <Typography
                variant="h3"
                sx={{
                  color: getTemperatureColor(weather.temperature),
                  fontWeight: 'bold',
                  lineHeight: 1,
                }}
              >
                {weather.temperature}°
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: 'rgba(255,255,255,0.9)' }}
              >
                {weather.condition}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography
                variant="body2"
                sx={{ color: 'rgba(255,255,255,0.7)' }}
              >
                湿度
              </Typography>
              <Typography
                variant="h6"
                sx={{ color: 'white', fontWeight: 'bold' }}
              >
                {weather.humidity}%
              </Typography>
            </Box>
            
            <Box sx={{ textAlign: 'center' }}>
              <Typography
                variant="body2"
                sx={{ color: 'rgba(255,255,255,0.7)' }}
              >
                体感
              </Typography>
              <Typography
                variant="h6"
                sx={{ color: 'white', fontWeight: 'bold' }}
              >
                {weather.temperature + Math.round(Math.random() * 4 - 2)}°
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};
