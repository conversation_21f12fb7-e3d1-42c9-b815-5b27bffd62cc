import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  TextField,
  InputAdornment,
  Fab,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';

import { RootState } from '../../store';
import {
  setNotes,
  setSearchQuery,
  setSelectedTags,
  setSortBy,
  setSortOrder,
  setLoading,
} from '../../store/slices/notesSlice';
import { DatabaseService } from '../../services/database';
import { EMOTION_TAGS } from '../../shared/constants';
import { NoteCard } from '../../components/Notes/NoteCard';
import { NoteEditor } from '../../components/Notes/NoteEditor';
import { useI18n } from '../../i18n';

export const NotesPage: React.FC = () => {
  const dispatch = useDispatch();
  const { t } = useI18n();
  
  const { profile } = useSelector((state: RootState) => state.user);
  const {
    notes,
    isLoading,
    searchQuery,
    selectedTags,
    sortBy,
    sortOrder,
  } = useSelector((state: RootState) => state.notes);

  const [showEditor, setShowEditor] = useState(false);
  const [editingNote, setEditingNote] = useState(null);

  useEffect(() => {
    if (profile?.id) {
      loadNotes();
    }
  }, [profile?.id]);

  const loadNotes = async () => {
    try {
      dispatch(setLoading(true));
      const notesData = await DatabaseService.getNotes(profile!.id);
      dispatch(setNotes(notesData));
    } catch (error) {
      console.error('Failed to load notes:', error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
  };

  const handleTagFilter = (tagId: string) => {
    const newTags = selectedTags.includes(tagId)
      ? selectedTags.filter(id => id !== tagId)
      : [...selectedTags, tagId];
    dispatch(setSelectedTags(newTags));
  };

  const handleSortChange = (field: string) => {
    if (sortBy === field) {
      dispatch(setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'));
    } else {
      dispatch(setSortBy(field as any));
      dispatch(setSortOrder('desc'));
    }
  };

  const handleCreateNote = () => {
    setEditingNote(null);
    setShowEditor(true);
  };

  const handleEditNote = (note: any) => {
    setEditingNote(note);
    setShowEditor(true);
  };

  const handleCloseEditor = () => {
    setShowEditor(false);
    setEditingNote(null);
  };

  const handleSaveNote = async () => {
    await loadNotes();
    handleCloseEditor();
  };

  // 过滤和排序笔记
  const filteredNotes = notes
    .filter(note => {
      // 搜索过滤
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        if (!note.title.toLowerCase().includes(query) &&
            !note.content.toLowerCase().includes(query)) {
          return false;
        }
      }

      // 标签过滤
      if (selectedTags.length > 0) {
        const noteEmotionIds = note.emotion.map(e => e.id);
        if (!selectedTags.some(tagId => noteEmotionIds.includes(tagId))) {
          return false;
        }
      }

      return true;
    })
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'title':
          aValue = a.title;
          bValue = b.title;
          break;
        case 'mood':
          aValue = a.mood;
          bValue = b.mood;
          break;
        case 'updatedAt':
          aValue = new Date(a.updatedAt).getTime();
          bValue = new Date(b.updatedAt).getTime();
          break;
        default: // createdAt
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            {t('notes.title')}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            记录心情，追踪情绪变化，让心理健康可视化
          </Typography>
        </Box>
      </motion.div>

      {/* 搜索和过滤栏 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder={t('notes.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>排序方式</InputLabel>
                <Select
                  value={sortBy}
                  label="排序方式"
                  onChange={(e) => handleSortChange(e.target.value)}
                >
                  <MenuItem value="createdAt">创建时间</MenuItem>
                  <MenuItem value="updatedAt">更新时间</MenuItem>
                  <MenuItem value="title">标题</MenuItem>
                  <MenuItem value="mood">心情指数</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => dispatch(setSelectedTags([]))}
              >
                清除筛选
              </Button>
            </Grid>
          </Grid>
        </Box>
      </motion.div>

      {/* 情绪标签过滤 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            按情绪筛选：
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {EMOTION_TAGS.map((tag) => (
              <Chip
                key={tag.id}
                label={`${tag.icon} ${tag.name}`}
                variant={selectedTags.includes(tag.id) ? 'filled' : 'outlined'}
                onClick={() => handleTagFilter(tag.id)}
                sx={{
                  bgcolor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                  color: selectedTags.includes(tag.id) ? 'white' : 'text.primary',
                  borderColor: tag.color,
                  '&:hover': {
                    bgcolor: tag.color,
                    color: 'white',
                  },
                }}
              />
            ))}
          </Box>
        </Box>
      </motion.div>

      {/* 笔记列表 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.6 }}
      >
        {filteredNotes.length === 0 ? (
          <Box
            sx={{
              textAlign: 'center',
              py: 8,
              color: 'text.secondary',
            }}
          >
            <Typography variant="h6" sx={{ mb: 2 }}>
              {searchQuery || selectedTags.length > 0 
                ? '没有找到匹配的笔记' 
                : t('notes.noNotes')
              }
            </Typography>
            <Typography variant="body2" sx={{ mb: 3 }}>
              {searchQuery || selectedTags.length > 0
                ? '尝试调整搜索条件或清除筛选'
                : '开始记录您的第一篇心理笔记吧'
              }
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateNote}
              sx={{
                background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
                },
              }}
            >
              {t('notes.newNote')}
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            <AnimatePresence>
              {filteredNotes.map((note, index) => (
                <Grid item xs={12} sm={6} md={4} key={note.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                  >
                    <NoteCard
                      note={note}
                      onEdit={() => handleEditNote(note)}
                      onDelete={loadNotes}
                    />
                  </motion.div>
                </Grid>
              ))}
            </AnimatePresence>
          </Grid>
        )}
      </motion.div>

      {/* 添加笔记按钮 */}
      <Fab
        color="primary"
        aria-label="add note"
        onClick={handleCreateNote}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
          },
        }}
      >
        <AddIcon />
      </Fab>

      {/* 笔记编辑器 */}
      <NoteEditor
        open={showEditor}
        note={editingNote}
        onClose={handleCloseEditor}
        onSave={handleSaveNote}
      />
    </Container>
  );
};
