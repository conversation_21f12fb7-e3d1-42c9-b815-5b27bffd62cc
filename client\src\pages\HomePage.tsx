import React, { useEffect, useState } from 'react';
import { Box, Grid, Card, CardContent, Typography, Button, Avatar } from '@mui/material';
import { motion } from 'framer-motion';
import { useSelector } from 'react-redux';
import { RootState } from '../store';

// 图标
import {
  Psychology as PsychologyIcon,
  Quiz as QuizIcon,
  PhotoLibrary as PhotoIcon,
  Build as ToolboxIcon,
  ViewInAr as VRIcon,
  Map as MapIcon,
} from '@mui/icons-material';

// 组件
import { WeatherWidget } from '../components/Widgets/WeatherWidget';
import { PoetryWidget } from '../components/Widgets/PoetryWidget';
import { StatsWidget } from '../components/Widgets/StatsWidget';
import { BackgroundVideo } from '../components/Background/BackgroundVideo';

const HomePage: React.FC = () => {
  const { profile } = useSelector((state: RootState) => state.user);
  const [currentTime, setCurrentTime] = useState(new Date());

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 功能模块数据
  const modules = [
    {
      title: '心理笔记',
      description: '记录心情，追踪情绪变化',
      icon: <PsychologyIcon />,
      color: '#87CEEB',
      path: '/notes',
    },
    {
      title: '心理测试',
      description: 'MBTI、大五人格等专业测试',
      icon: <QuizIcon />,
      color: '#FFB6C1',
      path: '/tests',
    },
    {
      title: '心理相册',
      description: '情绪记录，图片回忆',
      icon: <PhotoIcon />,
      color: '#98FB98',
      path: '/gallery',
    },
    {
      title: '百宝箱',
      description: '实用工具集合',
      icon: <ToolboxIcon />,
      color: '#DDA0DD',
      path: '/toolbox',
    },
    {
      title: '虚拟仿真',
      description: '3D心理场景体验',
      icon: <VRIcon />,
      color: '#F0E68C',
      path: '/vr',
    },
    {
      title: '地理分析',
      description: '地域心理学分析',
      icon: <MapIcon />,
      color: '#FFA07A',
      path: '/gis',
    },
  ];

  // 动画变体
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  return (
    <Box sx={{ position: 'relative', minHeight: '100vh', overflow: 'hidden' }}>
      {/* 背景视频 */}
      <BackgroundVideo />
      
      {/* 主要内容 */}
      <Box sx={{ position: 'relative', zIndex: 1, p: 3 }}>
        {/* 头部区域 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            {/* 用户头像和问候 */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <Avatar
                sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}
                src={profile?.avatar}
              >
                {profile?.username?.charAt(0).toUpperCase()}
              </Avatar>
              <Box>
                <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'white' }}>
                  你好，{profile?.username || '用户'}
                </Typography>
                <Typography variant="subtitle1" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                  {currentTime.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Typography>
              </Box>
            </Box>

            {/* 主标题 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <Typography
                variant="h2"
                sx={{
                  fontFamily: '"KaiTi", "楷体", serif',
                  fontWeight: 'bold',
                  color: 'white',
                  textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                  mb: 1,
                }}
              >
                心理
              </Typography>
            </motion.div>

            {/* 诗词组件 */}
            <PoetryWidget />
          </Box>
        </motion.div>

        {/* 功能模块网格 */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {modules.map((module, index) => (
              <Grid item xs={12} sm={6} md={4} key={module.title}>
                <motion.div variants={itemVariants}>
                  <Card
                    sx={{
                      height: '100%',
                      background: 'rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        background: 'rgba(255,255,255,0.2)',
                        boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
                      },
                    }}
                    onClick={() => {
                      // 导航到对应页面
                      window.location.hash = module.path;
                    }}
                  >
                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          borderRadius: '50%',
                          bgcolor: module.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 2,
                          color: 'white',
                        }}
                      >
                        {module.icon}
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{ fontWeight: 'bold', color: 'white', mb: 1 }}
                      >
                        {module.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: 'rgba(255,255,255,0.8)' }}
                      >
                        {module.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* 底部小部件区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <WeatherWidget />
            </Grid>
            <Grid item xs={12} md={8}>
              <StatsWidget />
            </Grid>
          </Grid>
        </motion.div>
      </Box>
    </Box>
  );
};

export { HomePage };
