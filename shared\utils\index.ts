// 共享工具函数

import { format, parseISO, isValid } from 'date-fns';
import { zhCN, enUS, ja } from 'date-fns/locale';

// 日期工具
export const dateUtils = {
  format: (date: Date | string, formatStr: string = 'yyyy-MM-dd HH:mm:ss', locale: string = 'zh-CN') => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    const localeMap = {
      'zh-CN': zhCN,
      'en-US': enUS,
      'ja-JP': ja,
    };
    
    return format(dateObj, formatStr, { locale: localeMap[locale as keyof typeof localeMap] || zhCN });
  },
  
  isValid: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isValid(dateObj);
  },
  
  now: () => new Date(),
  
  addDays: (date: Date, days: number) => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  },
  
  diffInDays: (date1: Date, date2: Date) => {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },
};

// 字符串工具
export const stringUtils = {
  truncate: (str: string, length: number, suffix: string = '...') => {
    if (str.length <= length) return str;
    return str.substring(0, length) + suffix;
  },
  
  capitalize: (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  },
  
  camelCase: (str: string) => {
    return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
  },
  
  kebabCase: (str: string) => {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
  },
  
  generateId: (prefix: string = '') => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
  },
  
  sanitizeFilename: (filename: string) => {
    return filename.replace(/[^a-z0-9.-]/gi, '_').toLowerCase();
  },
  
  extractHashtags: (text: string) => {
    const regex = /#[\w\u4e00-\u9fff]+/g;
    return text.match(regex) || [];
  },
};

// 数字工具
export const numberUtils = {
  formatBytes: (bytes: number, decimals: number = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  },
  
  formatNumber: (num: number, locale: string = 'zh-CN') => {
    return new Intl.NumberFormat(locale).format(num);
  },
  
  formatPercentage: (value: number, total: number, decimals: number = 1) => {
    if (total === 0) return '0%';
    return ((value / total) * 100).toFixed(decimals) + '%';
  },
  
  clamp: (value: number, min: number, max: number) => {
    return Math.min(Math.max(value, min), max);
  },
  
  random: (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
};

// 颜色工具
export const colorUtils = {
  hexToRgb: (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : null;
  },
  
  rgbToHex: (r: number, g: number, b: number) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  },
  
  adjustBrightness: (hex: string, percent: number) => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return hex;
    
    const adjust = (color: number) => {
      const adjusted = Math.round(color * (100 + percent) / 100);
      return Math.min(255, Math.max(0, adjusted));
    };
    
    return colorUtils.rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b));
  },
  
  getContrastColor: (hex: string) => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return '#000000';
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#ffffff';
  },
};

// 验证工具
export const validationUtils = {
  isEmail: (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  },
  
  isUrl: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  
  isPhoneNumber: (phone: string) => {
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phone);
  },
  
  isStrongPassword: (password: string) => {
    // 至少8位，包含大小写字母、数字和特殊字符
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return regex.test(password);
  },
  
  isValidJson: (str: string) => {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  },
};

// 存储工具
export const storageUtils = {
  set: (key: string, value: any) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch {
      return false;
    }
  },
  
  get: <T = any>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue || null;
    } catch {
      return defaultValue || null;
    }
  },
  
  remove: (key: string) => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch {
      return false;
    }
  },
  
  clear: () => {
    try {
      localStorage.clear();
      return true;
    } catch {
      return false;
    }
  },
  
  getSize: () => {
    let total = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  },
};

// 防抖和节流
export const throttleUtils = {
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },
  
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, wait);
      }
    };
  },
};

// 错误处理
export const errorUtils = {
  createError: (code: string, message: string, details?: any) => {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
    };
  },
  
  logError: (error: any, context?: string) => {
    console.error(`[${context || 'Error'}]`, error);
    // 这里可以添加错误上报逻辑
  },
  
  handleAsyncError: async <T>(
    promise: Promise<T>,
    errorMessage?: string
  ): Promise<[T | null, any]> => {
    try {
      const data = await promise;
      return [data, null];
    } catch (error) {
      errorUtils.logError(error, errorMessage);
      return [null, error];
    }
  },
};

// 设备检测
export const deviceUtils = {
  isMobile: () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },
  
  isTablet: () => {
    return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
  },
  
  isDesktop: () => {
    return !deviceUtils.isMobile() && !deviceUtils.isTablet();
  },
  
  getOS: () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  },
  
  getBrowser: () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  },
  
  getScreenSize: () => {
    return {
      width: window.screen.width,
      height: window.screen.height,
      availWidth: window.screen.availWidth,
      availHeight: window.screen.availHeight,
    };
  },
};

// 性能监控
export const performanceUtils = {
  measureTime: (name: string) => {
    const start = performance.now();
    return {
      end: () => {
        const duration = performance.now() - start;
        console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
        return duration;
      },
    };
  },
  
  getMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: numberUtils.formatBytes(memory.usedJSHeapSize),
        total: numberUtils.formatBytes(memory.totalJSHeapSize),
        limit: numberUtils.formatBytes(memory.jsHeapSizeLimit),
      };
    }
    return null;
  },
  
  getFPS: (callback: (fps: number) => void) => {
    let frames = 0;
    let lastTime = performance.now();
    
    const countFrames = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        callback(Math.round((frames * 1000) / (currentTime - lastTime)));
        frames = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrames);
    };
    
    requestAnimationFrame(countFrames);
  },
};
