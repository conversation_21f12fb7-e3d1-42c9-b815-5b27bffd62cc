import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PsychologyNote } from '../../shared/types';

interface NotesState {
  notes: PsychologyNote[];
  currentNote: PsychologyNote | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  selectedTags: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'title' | 'mood';
  sortOrder: 'asc' | 'desc';
}

const initialState: NotesState = {
  notes: [],
  currentNote: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  selectedTags: [],
  sortBy: 'createdAt',
  sortOrder: 'desc',
};

const notesSlice = createSlice({
  name: 'notes',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setNotes: (state, action: PayloadAction<PsychologyNote[]>) => {
      state.notes = action.payload;
    },
    addNote: (state, action: PayloadAction<PsychologyNote>) => {
      state.notes.unshift(action.payload);
    },
    updateNote: (state, action: PayloadAction<PsychologyNote>) => {
      const index = state.notes.findIndex(note => note.id === action.payload.id);
      if (index !== -1) {
        state.notes[index] = action.payload;
      }
    },
    deleteNote: (state, action: PayloadAction<string>) => {
      state.notes = state.notes.filter(note => note.id !== action.payload);
    },
    setCurrentNote: (state, action: PayloadAction<PsychologyNote | null>) => {
      state.currentNote = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedTags: (state, action: PayloadAction<string[]>) => {
      state.selectedTags = action.payload;
    },
    setSortBy: (state, action: PayloadAction<NotesState['sortBy']>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<NotesState['sortOrder']>) => {
      state.sortOrder = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setNotes,
  addNote,
  updateNote,
  deleteNote,
  setCurrentNote,
  setSearchQuery,
  setSelectedTags,
  setSortBy,
  setSortOrder,
} = notesSlice.actions;

export default notesSlice.reducer;
