// 简化的类型定义
export interface MediaFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: Date;
  userId: string;
  metadata?: any;
}

export interface PsychologyNote {
  id: string;
  title: string;
  content: string;
  emotion: any[];
  mood: number;
  weather?: any;
  location?: any;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  attachments: any[];
  userId: string;
}

export interface TestResult {
  id: string;
  testId: string;
  userId: string;
  answers: any[];
  scores: any[];
  completedAt: Date;
  timeSpent: number;
}

export interface PsychologyTest {
  id: string;
  name: string;
  description: string;
  questions: any[];
  type: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
}

export interface UserPreferences {
  theme: string;
  language: string;
  notifications: boolean;
}

export interface VRScene {
  id: string;
  name: string;
  description: string;
  category: string;
}

export interface VRSession {
  id: string;
  sceneId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
}

export interface EmotionHeatMapData {
  id: string;
  location: any;
  emotion: string;
  intensity: number;
  count: number;
  timestamp: Date;
  userId: string;
}

export interface POI {
  id: string;
  name: string;
  location: any;
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  address?: string;
}