import React, { useEffect, useState } from 'react';
import { Card, CardContent, Typography, Box, Grid, LinearProgress } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Psychology as NotesIcon,
  Quiz as TestsIcon,
  PhotoLibrary as PhotosIcon,
  Mood as MoodIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import { DatabaseService } from '../../services/database';

interface StatsData {
  notesCount: number;
  testResultsCount: number;
  mediaFilesCount: number;
  averageMood: number;
}

export const StatsWidget: React.FC = () => {
  const { profile } = useSelector((state: RootState) => state.user);
  const [stats, setStats] = useState<StatsData>({
    notesCount: 0,
    testResultsCount: 0,
    mediaFilesCount: 0,
    averageMood: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (profile?.id) {
      fetchStats();
    }
  }, [profile?.id]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const statsData = await DatabaseService.getStats(profile!.id);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
      // 使用模拟数据
      setStats({
        notesCount: Math.floor(Math.random() * 50) + 10,
        testResultsCount: Math.floor(Math.random() * 20) + 5,
        mediaFilesCount: Math.floor(Math.random() * 100) + 20,
        averageMood: Math.round((Math.random() * 4 + 6) * 10) / 10, // 6-10之间
      });
    } finally {
      setLoading(false);
    }
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 8) return '#4CAF50'; // 绿色 - 很好
    if (mood >= 6) return '#FFC107'; // 黄色 - 一般
    if (mood >= 4) return '#FF9800'; // 橙色 - 不太好
    return '#F44336'; // 红色 - 很差
  };

  const getMoodText = (mood: number) => {
    if (mood >= 8) return '心情很好';
    if (mood >= 6) return '心情不错';
    if (mood >= 4) return '心情一般';
    return '需要关注';
  };

  const statItems = [
    {
      icon: <NotesIcon sx={{ fontSize: 24 }} />,
      label: '心理笔记',
      value: stats.notesCount,
      color: '#87CEEB',
      unit: '篇',
    },
    {
      icon: <TestsIcon sx={{ fontSize: 24 }} />,
      label: '心理测试',
      value: stats.testResultsCount,
      color: '#FFB6C1',
      unit: '次',
    },
    {
      icon: <PhotosIcon sx={{ fontSize: 24 }} />,
      label: '心理相册',
      value: stats.mediaFilesCount,
      color: '#98FB98',
      unit: '张',
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      <Card
        sx={{
          height: 200,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        }}
      >
        <CardContent sx={{ height: '100%', p: 3 }}>
          <Typography
            variant="h6"
            sx={{ color: 'white', fontWeight: 'bold', mb: 2 }}
          >
            数据统计
          </Typography>

          <Grid container spacing={2} sx={{ height: 'calc(100% - 40px)' }}>
            {/* 统计项目 */}
            <Grid item xs={9}>
              <Grid container spacing={1}>
                {statItems.map((item, index) => (
                  <Grid item xs={4} key={item.label}>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.3 }}
                    >
                      <Box
                        sx={{
                          textAlign: 'center',
                          p: 1,
                          borderRadius: 2,
                          background: 'rgba(255, 255, 255, 0.1)',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                        }}
                      >
                        <Box
                          sx={{
                            color: item.color,
                            mb: 0.5,
                            display: 'flex',
                            justifyContent: 'center',
                          }}
                        >
                          {item.icon}
                        </Box>
                        
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.1 + 0.5 }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              color: 'white',
                              fontWeight: 'bold',
                              lineHeight: 1,
                              mb: 0.5,
                            }}
                          >
                            {loading ? '-' : item.value}
                          </Typography>
                        </motion.div>
                        
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'rgba(255,255,255,0.8)',
                            fontSize: '0.7rem',
                          }}
                        >
                          {item.label}
                        </Typography>
                      </Box>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Grid>

            {/* 心情指数 */}
            <Grid item xs={3}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Box
                  sx={{
                    textAlign: 'center',
                    p: 1,
                    borderRadius: 2,
                    background: 'rgba(255, 255, 255, 0.1)',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <MoodIcon
                    sx={{
                      fontSize: 24,
                      color: getMoodColor(stats.averageMood),
                      mb: 0.5,
                    }}
                  />
                  
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.8, type: 'spring' }}
                  >
                    <Typography
                      variant="h6"
                      sx={{
                        color: getMoodColor(stats.averageMood),
                        fontWeight: 'bold',
                        lineHeight: 1,
                        mb: 0.5,
                      }}
                    >
                      {loading ? '-' : stats.averageMood}
                    </Typography>
                  </motion.div>
                  
                  <Typography
                    variant="caption"
                    sx={{
                      color: 'rgba(255,255,255,0.8)',
                      fontSize: '0.7rem',
                      mb: 0.5,
                    }}
                  >
                    平均心情
                  </Typography>
                  
                  {!loading && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 1 }}
                    >
                      <LinearProgress
                        variant="determinate"
                        value={(stats.averageMood / 10) * 100}
                        sx={{
                          height: 4,
                          borderRadius: 2,
                          bgcolor: 'rgba(255,255,255,0.2)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: getMoodColor(stats.averageMood),
                            borderRadius: 2,
                          },
                        }}
                      />
                      
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'rgba(255,255,255,0.7)',
                          fontSize: '0.6rem',
                          mt: 0.5,
                          display: 'block',
                        }}
                      >
                        {getMoodText(stats.averageMood)}
                      </Typography>
                    </motion.div>
                  )}
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </motion.div>
  );
};
