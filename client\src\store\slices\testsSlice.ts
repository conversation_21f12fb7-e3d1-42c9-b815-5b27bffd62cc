import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PsychologyTest, TestResult } from '@shared/types';

interface TestsState {
  tests: PsychologyTest[];
  results: TestResult[];
  currentTest: PsychologyTest | null;
  currentResult: TestResult | null;
  isLoading: boolean;
  error: string | null;
  progress: {
    testId: string;
    currentQuestion: number;
    totalQuestions: number;
    answers: Record<string, string>;
    startTime: Date | null;
  } | null;
}

const initialState: TestsState = {
  tests: [],
  results: [],
  currentTest: null,
  currentResult: null,
  isLoading: false,
  error: null,
  progress: null,
};

const testsSlice = createSlice({
  name: 'tests',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setTests: (state, action: PayloadAction<PsychologyTest[]>) => {
      state.tests = action.payload;
    },
    setResults: (state, action: PayloadAction<TestResult[]>) => {
      state.results = action.payload;
    },
    addResult: (state, action: PayloadAction<TestResult>) => {
      state.results.unshift(action.payload);
    },
    setCurrentTest: (state, action: PayloadAction<PsychologyTest | null>) => {
      state.currentTest = action.payload;
    },
    setCurrentResult: (state, action: PayloadAction<TestResult | null>) => {
      state.currentResult = action.payload;
    },
    startTest: (state, action: PayloadAction<{ testId: string; totalQuestions: number }>) => {
      state.progress = {
        testId: action.payload.testId,
        currentQuestion: 0,
        totalQuestions: action.payload.totalQuestions,
        answers: {},
        startTime: new Date(),
      };
    },
    answerQuestion: (state, action: PayloadAction<{ questionId: string; optionId: string }>) => {
      if (state.progress) {
        state.progress.answers[action.payload.questionId] = action.payload.optionId;
        state.progress.currentQuestion += 1;
      }
    },
    resetProgress: (state) => {
      state.progress = null;
    },
  },
});

export const {
  setLoading,
  setError,
  setTests,
  setResults,
  addResult,
  setCurrentTest,
  setCurrentResult,
  startTest,
  answerQuestion,
  resetProgress,
} = testsSlice.actions;

export default testsSlice.reducer;
