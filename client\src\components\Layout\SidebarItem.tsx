import React from 'react';
import { ListItem, ListItemButton, ListItemIcon, ListItemText, Box } from '@mui/material';
import { motion } from 'framer-motion';

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  path: string;
  isActive: boolean;
  onClick: () => void;
  badge?: number;
}

export const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  path,
  isActive,
  onClick,
  badge,
}) => {
  return (
    <ListItem disablePadding sx={{ mb: 0.5 }}>
      <motion.div
        style={{ width: '100%' }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <ListItemButton
          onClick={onClick}
          sx={{
            borderRadius: 2,
            minHeight: 48,
            position: 'relative',
            overflow: 'hidden',
            background: isActive 
              ? 'linear-gradient(135deg, rgba(135, 206, 235, 0.2), rgba(152, 251, 152, 0.2))'
              : 'transparent',
            color: isActive ? 'primary.main' : 'text.primary',
            '&:hover': {
              background: isActive
                ? 'linear-gradient(135deg, rgba(135, 206, 235, 0.3), rgba(152, 251, 152, 0.3))'
                : 'rgba(0, 0, 0, 0.04)',
            },
            '&::before': isActive ? {
              content: '""',
              position: 'absolute',
              left: 0,
              top: 0,
              bottom: 0,
              width: 4,
              background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
              borderRadius: '0 2px 2px 0',
            } : {},
          }}
        >
          <ListItemIcon
            sx={{
              color: 'inherit',
              minWidth: 40,
              '& svg': {
                fontSize: '1.25rem',
              },
            }}
          >
            {icon}
          </ListItemIcon>
          
          <ListItemText
            primary={label}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: isActive ? 600 : 400,
            }}
          />

          {/* 徽章 */}
          {badge && badge > 0 && (
            <Box
              sx={{
                minWidth: 20,
                height: 20,
                borderRadius: '10px',
                bgcolor: 'error.main',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.75rem',
                fontWeight: 'bold',
              }}
            >
              {badge > 99 ? '99+' : badge}
            </Box>
          )}

          {/* 活跃状态指示器 */}
          {isActive && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 500, damping: 30 }}
              style={{
                position: 'absolute',
                right: 12,
                width: 6,
                height: 6,
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
              }}
            />
          )}
        </ListItemButton>
      </motion.div>
    </ListItem>
  );
};
