import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  TextField,
  Chip,
  Grid,
  Paper,
  IconButton,
  Slider,
  FormControl,
  InputLabel,
  OutlinedInput,
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  LocationOn as LocationIcon,
  Schedule as TimeIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useDispatch } from 'react-redux';

import { MediaFile, EmotionTag } from '../../shared/types';
import { DatabaseService } from '../../services/database';
import { addNotification } from '../../store/slices/appSlice';
import { EMOTION_TAGS } from '../../shared/constants';
import { dateUtils } from '../../shared/utils';

interface ImageEditorProps {
  open: boolean;
  image: MediaFile | null;
  onClose: () => void;
  onSave: () => void;
}

export const ImageEditor: React.FC<ImageEditorProps> = ({
  open,
  image,
  onClose,
  onSave,
}) => {
  const dispatch = useDispatch();
  
  const [formData, setFormData] = useState({
    description: '',
    emotions: [] as EmotionTag[],
    tags: [] as string[],
    mood: 5,
  });
  
  const [tagInput, setTagInput] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (image) {
      setFormData({
        description: image.metadata?.description || '',
        emotions: image.metadata?.emotions || [],
        tags: image.metadata?.tags || [],
        mood: image.metadata?.mood || 5,
      });
    }
  }, [image]);

  const handleEmotionToggle = (emotionTag: typeof EMOTION_TAGS[0]) => {
    const emotion: EmotionTag = {
      id: emotionTag.id,
      name: emotionTag.name,
      color: emotionTag.color,
      intensity: 5,
    };

    setFormData(prev => {
      const exists = prev.emotions.find(e => e.id === emotion.id);
      if (exists) {
        return {
          ...prev,
          emotions: prev.emotions.filter(e => e.id !== emotion.id),
        };
      } else {
        return {
          ...prev,
          emotions: [...prev.emotions, emotion],
        };
      }
    });
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleSave = async () => {
    if (!image) return;

    try {
      setSaving(true);
      
      const updatedMetadata = {
        ...image.metadata,
        description: formData.description,
        emotions: formData.emotions,
        tags: formData.tags,
        mood: formData.mood,
      };

      // 更新数据库中的图片信息
      await DatabaseService.updateMediaFile(image.id, {
        metadata: updatedMetadata,
      });

      dispatch(addNotification({
        type: 'success',
        title: '保存成功',
        message: '图片信息已更新',
      }));

      onSave();
      onClose();
    } catch (error) {
      console.error('Failed to save image metadata:', error);
      dispatch(addNotification({
        type: 'error',
        title: '保存失败',
        message: '更新图片信息失败',
      }));
    } finally {
      setSaving(false);
    }
  };

  const getMoodText = (mood: number) => {
    if (mood >= 8) return '心情很好';
    if (mood >= 6) return '心情不错';
    if (mood >= 4) return '心情一般';
    return '心情不佳';
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 8) return '#4CAF50';
    if (mood >= 6) return '#FFC107';
    if (mood >= 4) return '#FF9800';
    return '#F44336';
  };

  if (!image) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6">编辑图片信息</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* 图片预览 */}
          <Grid item xs={12} md={5}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <Paper
                sx={{
                  p: 2,
                  textAlign: 'center',
                  bgcolor: 'grey.50',
                }}
              >
                <img
                  src={image.url}
                  alt={image.filename}
                  style={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: 300,
                    objectFit: 'contain',
                    borderRadius: 8,
                  }}
                />
                
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" noWrap>
                    {image.originalName}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                    <TimeIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="caption" color="text.secondary">
                      {dateUtils.format(image.uploadedAt, 'YYYY-MM-DD HH:mm')}
                    </Typography>
                  </Box>
                  
                  {image.metadata?.location && (
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 0.5 }}>
                      <LocationIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {image.metadata.location.address || '位置信息'}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </motion.div>
          </Grid>

          {/* 编辑表单 */}
          <Grid item xs={12} md={7}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {/* 描述 */}
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="图片描述"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="记录这张图片的故事..."
                />

                {/* 心情指数 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    心情指数: {formData.mood}/10 - {getMoodText(formData.mood)}
                  </Typography>
                  <Slider
                    value={formData.mood}
                    onChange={(e, value) => setFormData(prev => ({ ...prev, mood: value as number }))}
                    min={1}
                    max={10}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    sx={{
                      color: getMoodColor(formData.mood),
                      '& .MuiSlider-thumb': {
                        bgcolor: getMoodColor(formData.mood),
                      },
                      '& .MuiSlider-track': {
                        bgcolor: getMoodColor(formData.mood),
                      },
                    }}
                  />
                </Box>

                {/* 情绪标签 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    情绪标签
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {EMOTION_TAGS.map((tag) => (
                      <Chip
                        key={tag.id}
                        label={`${tag.icon} ${tag.name}`}
                        variant={formData.emotions.some(e => e.id === tag.id) ? 'filled' : 'outlined'}
                        onClick={() => handleEmotionToggle(tag)}
                        sx={{
                          bgcolor: formData.emotions.some(e => e.id === tag.id) ? tag.color : 'transparent',
                          color: formData.emotions.some(e => e.id === tag.id) ? 'white' : 'text.primary',
                          borderColor: tag.color,
                          '&:hover': {
                            bgcolor: tag.color,
                            color: 'white',
                          },
                        }}
                      />
                    ))}
                  </Box>
                </Box>

                {/* 自定义标签 */}
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    自定义标签
                  </Typography>
                  
                  {formData.tags.length > 0 && (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {formData.tags.map((tag) => (
                        <Chip
                          key={tag}
                          label={`#${tag}`}
                          onDelete={() => handleRemoveTag(tag)}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  )}
                  
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      size="small"
                      placeholder="添加标签..."
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                      sx={{ flex: 1 }}
                    />
                    <Button onClick={handleAddTag} variant="outlined" size="small">
                      添加
                    </Button>
                  </Box>
                </Box>
              </Box>
            </motion.div>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} disabled={saving}>
          取消
        </Button>
        
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={saving}
          startIcon={<SaveIcon />}
          sx={{
            background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
            },
          }}
        >
          {saving ? '保存中...' : '保存'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
