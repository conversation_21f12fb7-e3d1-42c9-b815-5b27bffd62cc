# LENGSHANG 跨平台心理分析应用

一款融合前沿技术与心理学理论的全面心理健康工具，致力于为用户提供安全、私密且富有洞察力的个人心理分析空间。

## 🌟 项目特色

- **跨平台支持**: 基于 Electron + Tauri 混合架构，支持 Windows、macOS、Linux
- **现代化技术栈**: React 18 + TypeScript + Tailwind CSS
- **丰富的功能模块**: 心理笔记、心理测试、VR仿真、地理分析等
- **多语言支持**: 中文、英文、日文界面
- **隐私保护**: 端到端加密，本地数据存储优先
- **管理后台**: 完整的内容管理和数据分析系统

## 🏗️ 项目架构

```
lengshang-psychology-app/
├── client/                 # 客户端应用（Electron/Tauri）
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   └── dist/              # 构建输出
├── admin-backend/          # 管理后台系统
│   ├── src/               # 后端源代码
│   └── dist/              # 构建输出
├── web-launcher/           # 网页版启动器
│   ├── src/               # 启动器源代码
│   └── dist/              # 构建输出
├── shared/                 # 共享组件和工具
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   └── constants/         # 常量定义
├── docs/                   # 项目文档
└── scripts/                # 构建和部署脚本
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- Git

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/lengshang/psychology-app.git
cd lengshang-psychology-app

# 安装所有依赖
npm run setup
```

### 开发模式

```bash
# 启动所有服务（客户端、后台、启动器）
npm run dev

# 或者单独启动
npm run dev:client    # 客户端应用
npm run dev:admin     # 管理后台
npm run dev:launcher  # 网页启动器
```

### 构建生产版本

```bash
# 构建所有模块
npm run build

# 构建 Electron 应用
npm run electron:build

# 构建 Tauri 应用
npm run tauri:build
```

## 📋 核心功能

### 🧠 心理分析系统
- **心理笔记**: Markdown编辑器，支持情绪标签和地理位置
- **心理测试**: MBTI、大五人格、霍兰德职业兴趣等标准测试
- **结果分析**: 雷达图、柱状图等多维度可视化分析

### 🎮 虚拟仿真系统
- **冥想花园**: 虚拟禅意花园，配合呼吸引导
- **情绪释放空间**: 可交互的虚拟物体，安全宣泄负面情绪
- **记忆宫殿**: 3D空间记忆训练
- **社交模拟**: 虚拟社交场景练习

### 🗺️ 地理分析模块
- **情绪地图**: 基于地理位置的情绪热力图
- **活动轨迹**: 个人活动与情绪关联分析
- **健康指数**: 地域心理健康综合评估

### 🧰 百宝箱工具集
- **智能待办**: 四象限时间管理，番茄工作法
- **音乐播放器**: 情绪推荐，双耳节拍治疗音频
- **天气关联**: 天气与心情相关性分析
- **心理游戏**: 认知训练，注意力练习

## 🛠️ 技术栈

### 前端技术
- **框架**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion + Lottie + Three.js
- **图表**: ECharts + Recharts + Victory
- **地图**: Leaflet + OpenLayers + D3.js

### 桌面应用
- **Electron**: 成熟的跨平台桌面应用框架
- **Tauri**: 轻量级、高性能的桌面应用框架

### 后端技术
- **Node.js**: 服务器运行环境
- **Express/NestJS**: Web应用框架
- **PostgreSQL/MongoDB**: 数据库
- **JWT**: 身份认证

### 开发工具
- **TypeScript**: 类型安全
- **ESLint + Prettier**: 代码规范
- **Jest**: 单元测试
- **Cypress**: 端到端测试

## 🔒 隐私与安全

- **本地优先**: 敏感数据优先存储在本地
- **端到端加密**: 云同步数据采用端到端加密
- **匿名化处理**: 聚合分析数据完全匿名化
- **GDPR合规**: 符合数据保护法规要求

## 📱 支持平台

- **Windows**: 7/8/10/11 (x86/x64/ARM64)
- **macOS**: 10.15+ (Intel/Apple Silicon)
- **Linux**: Ubuntu 18.04+/Debian 10+/Deepin 20.3+

## 🌍 国际化

- 简体中文 (zh-CN)
- 英语 (en-US)  
- 日语 (ja-JP)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系我们

- 官网: https://lengshang.app
- 邮箱: <EMAIL>
- 文档: https://docs.lengshang.app

---

**LENGSHANG** - 让心理健康触手可及 💚
