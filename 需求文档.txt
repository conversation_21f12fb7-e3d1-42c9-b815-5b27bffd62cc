# 跨平台心理分析应用完善需求文档

## 一、技术架构与基础规范

### 核心技术栈
- **框架**: Electron + Tauri 混合架构
- **前端**: React 18 + TypeScript + Tailwind CSS
- **动画**: Framer Motion + Lottie + Three.js
- **地图**: Leaflet + OpenLayers + D3.js
- **数据可视化**: ECharts + Recharts + Victory
- **音频**: Web Audio API + Tone.js
- **视频**: Video.js + HLS.js

### 支持平台
- Windows 7-11 (x86/x64/ARM64)
- macOS 10.15+ (Intel/Apple Silicon)
- Linux (Ubuntu 18.04+/Debian 10+/Deepin 20.3+)

## 二、界面设计规范与主题系统

### 主界面布局优化
```
┌─────────────────────────────────────────────────┐
│ LENGSHANG Logo │ 导航菜单 │ 语言切换 │ 用户头像 │
├─────────────────────────────────────────────────┤
│                                                 │
│     背景视频区域 (Wallpaper.mp4/wall.mp4)      │
│   ┌─吉卜力角色动态展示─┐  ┌─实时天气信息─┐    │
│   │   龙猫、千寻等    │  │  温度/湿度    │    │
│   │   视差滚动效果    │  │  地理位置     │    │
│   └─────────────────┘  └─────────────┘    │
│                                                 │
│        主标题"心理" (毛笔字体+打字机效果)        │
│        副标题"随机古诗（API返回）" (淡入动画)       │
│                                                 │
├─────────────────────────────────────────────────┤
│ 功能模块快捷入口 │ 数据统计面板 │ 系统状态指示器 │
└─────────────────────────────────────────────────┘
```

### 主题配色系统
**亮色主题**:
- 主色调: #87CEEB → #FFFFFF (天空蓝渐变)
- 辅助色: #FFB6C1 (樱花粉) / #98FB98 (薄荷绿)
- 文字色: #2F4F4F (深灰蓝)

**暗色主题**:
- 主色调: #1E1E2E → #313244 (深紫灰渐变)
- 辅助色: #F38BA8 (玫瑰金) / #A6E3A1 (薄荷绿)
- 文字色: #CDD6F4 (淡紫白)

### 导航菜单扩展
- 首页 (Home) - 数据看板
- 心理笔记 (Psychology Notes) - Markdown编辑器
- 心理测试 (Mental Test) - MBTI/大五人格/霍兰德
- 心理相册 (Photo Gallery) - 情绪记录相册
- 百宝箱 (Toolbox) - 实用工具集合
- **虚拟仿真 (VR Simulation)** - 3D心理场景体验
- **地理分析 (GIS Analysis)** - 地域心理学分析
- 设置 (Settings) - 系统配置

## 三、核心功能模块详细设计

### 3.1 心理分析系统 (Enhanced)

#### 心理笔记模块
```typescript
interface PsychologyNote {
  id: string;
  title: string;
  content: string; // Markdown格式
  emotion: EmotionTag[];
  mood: number; // 1-10心情指数
  weather: WeatherInfo;
  location: GeoLocation;
  createdAt: Date;
  tags: string[];
  attachments: MediaFile[];
}
```

**功能特性**:
- 富文本Markdown编辑器 (支持LaTeX数学公式)
- 情绪标签系统 (快乐/焦虑/愤怒/悲伤等12种基础情绪)
- 心情指数滑块 (1-10分制，带动画反馈)
- 自动地理位置记录
- 语音转文字输入 (支持中英双语)
- 图片OCR文字识别
- 情绪热力图分析 (按时间/地点分布)

#### 心理测试模块
**测试类型**:
1. **MBTI人格测试** (93题完整版)
2. **大五人格测试** (44题NEO-PI简化版)
3. **霍兰德职业兴趣测试** (60题标准版)
4. **抑郁自评量表** (SDS-20题)
5. **焦虑自评量表** (SAS-20题)
6. **压力测试** (PSS-14题)

**结果分析系统**:
```javascript
// 雷达图分析
const radarConfig = {
  data: [
    { dimension: '外向性', value: 85 },
    { dimension: '开放性', value: 72 },
    { dimension: '尽责性', value: 91 },
    { dimension: '宜人性', value: 78 },
    { dimension: '神经质', value: 34 }
  ],
  animation: true,
  interactivity: true
};

// 柱状图对比分析
const barChartConfig = {
  data: {
    current: [85, 72, 91, 78, 34],
    average: [70, 65, 75, 80, 45],
    optimal: [80, 75, 85, 85, 30]
  },
  labels: ['外向性', '开放性', '尽责性', '宜人性', '神经质']
};
```

### 3.2 虚拟仿真系统 (新增)

#### 3D心理场景体验
**技术实现**: Three.js + WebXR API

**场景类型**:
1. **冥想花园**: 虚拟禅意花园，配合呼吸引导
2. **情绪释放空间**: 可破坏的虚拟物体，释放负面情绪
3. **记忆宫殿**: 3D空间记忆训练
4. **社交模拟**: 虚拟社交场景练习
5. **恐惧暴露**: 渐进式恐惧症治疗场景

```javascript
// VR场景配置
const VRScenes = {
  meditation: {
    environment: 'zen_garden',
    audio: 'nature_sounds',
    interaction: 'breathing_guide',
    duration: '5-30min'
  },
  emotion_release: {
    environment: 'break_room',
    objects: ['plates', 'pillows', 'balloons'],
    physics: true,
    haptic_feedback: true
  },
  memory_palace: {
    environment: 'classical_library',
    memory_technique: 'loci_method',
    difficulty_levels: 5
  }
};
```

### 3.3 地理信息系统 (GIS) 分析模块 (新增)

#### 地域心理学分析
**技术栈**: Leaflet + D3.js + Turf.js

**核心功能**:

1. **情绪地图热力图**
```javascript
const emotionHeatMap = {
  dataSource: 'user_notes_geolocation',
  layers: [
    { emotion: 'happiness', color: '#FFD700', weight: 0.8 },
    { emotion: 'sadness', color: '#4169E1', weight: 0.6 },
    { emotion: 'anxiety', color: '#FF6347', weight: 0.9 },
    { emotion: 'calm', color: '#98FB98', weight: 0.5 }
  ],
  clustering: true,
  temporal: true // 支持时间维度分析
};
```

2. **地域心理健康指数**
- 基于用户数据生成区域心理健康评分
- 环境因素关联分析 (空气质量/噪音/绿化率)
- 社会经济指标对比
- 季节性情绪波动分析

3. **个人活动轨迹分析**
- GPS轨迹记录与情绪关联
- 常驻地点情绪模式识别
- 出行对心理状态影响分析
- 推荐心理健康友好场所

**GIS可视化组件**:
```typescript
interface GISAnalysis {
  emotionHeatMap: HeatMapLayer;
  trajectoryAnalysis: PathLayer;
  wellnessIndex: ChoroplethLayer;
  poiRecommendation: PointLayer;
  weatherCorrelation: WeatherLayer;
}
```

### 3.4 百宝箱工具集 (优化)

#### 核心工具模块
1. **智能待办清单**
   - 四象限时间管理法
   - 优先级智能排序
   - 番茄工作法集成
   - 语音快速添加

2. **高级音乐播放器**
```javascript
const musicPlayer = {
  features: {
    lyric_sync: true, // 逐字歌词同步
    audio_visualization: true, // Web Audio频谱
    mood_playlist: true, // 根据心情推荐
    binaural_beats: true, // 双耳节拍治疗音频
    nature_sounds: true // 自然白噪音
  },
  apis: [
    'https://api.cenguigui.cn/api/music/kuwo/',
    'https://zj.v.api.aa1.cn/api/qqmusic/',
    'https://www.hhlqilongzhu.cn/api/joox/'
  ]
};
```

3. **天气心情关联分析**
   - 实时天气获取
   - 气象与情绪相关性分析
   - 天气预警心理准备建议

4. **便签与心情日记**
   - 手写识别 (支持触控笔)
   - 情绪色彩编码
   - 云端同步备份

5. **心理小游戏集合**
   - 认知训练游戏
   - 注意力集中练习
   - 记忆力提升游戏
   - 情绪调节小游戏

## 四、API集成与数据接口

### 4.1 现有API优化
```javascript
const APIEndpoints = {
  // 图片资源
  bing_wallpaper: 'https://api.cenguigui.cn/api/bing/api.php?type=json',
  anime_images: 'https://api.cenguigui.cn/api/pic/',
  
  // 音乐服务
  random_music: 'https://api.uomg.com/api/rand.music?sort=热歌榜&format=json',
  kuwo_search: 'https://api.cenguigui.cn/api/music/kuwo/KoWo_Dg.php',
  qq_music: 'https://zj.v.api.aa1.cn/api/qqmusic/demo.php',
  
  // 内容服务
  daily_poetry: 'https://api.cenguigui.cn/api/yyan/api.php',
  history_today: 'https://v2.api-m.com/api/history',
  daily_news: 'https://www.hhlqilongzhu.cn/api/60s.php',
  
  // 视频内容
  random_videos: ['https://api.hn/video.php?return=json', 'https://api.heylie.cn/api/video?v=xq']
};
```

### 4.2 新增API需求
```javascript
const newAPIs = {
  // 心理健康
  mental_health: {
    mood_analysis: '/api/psychology/mood-analysis',
    personality_test: '/api/psychology/personality-test',
    therapy_resources: '/api/psychology/therapy-resources'
  },
  
  // 地理信息
  geolocation: {
    geocoding: '/api/geo/geocoding',
    weather_correlation: '/api/geo/weather-emotion',
    poi_wellness: '/api/geo/wellness-spots'
  },
  
  // AI服务
  ai_services: {
    nlp_emotion: '/api/ai/emotion-detection',
    image_analysis: '/api/ai/image-emotion',
    voice_analysis: '/api/ai/voice-emotion'
  }
};
```

## 五、用户体验与交互设计

### 5.1 动画系统优化
```javascript
const animationConfig = {
  page_transitions: {
    duration: 600,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    effects: ['fade', 'slide', 'scale']
  },
  
  micro_interactions: {
    button_hover: { scale: 1.05, duration: 200 },
    card_elevation: { shadow: '0 8px 25px rgba(0,0,0,0.15)' },
    loading_states: 'skeleton + shimmer'
  },
  
  data_visualization: {
    chart_animations: true,
    progressive_loading: true,
    interactive_tooltips: true
  }
};
```

### 5.2 无障碍性设计
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 文字大小调节
- 色盲友好配色

### 5.3 响应式布局
```css
/* 响应式断点 */
@media (max-width: 768px) { /* 平板适配 */ }
@media (max-width: 480px) { /* 手机适配 */ }
@media (min-width: 1920px) { /* 4K显示器优化 */ }
```

## 六、数据管理与安全

### 6.1 本地数据存储
```javascript
const dataStructure = {
  user_profiles: 'IndexedDB',
  psychology_notes: 'IndexedDB + File System',
  test_results: 'Encrypted SQLite',
  media_files: 'Local File System',
  settings: 'JSON Config Files'
};
```

### 6.2 云端同步 (可选)
- 端到端加密同步
- 增量数据备份
- 多设备数据一致性
- 离线优先策略

### 6.3 隐私保护
- XChaCha20-Poly1305 加密
- 本地数据匿名化
- 用户数据导出权
- GDPR 合规支持

## 七、性能优化方案

### 7.1 渲染性能
```javascript
const performanceOptimizations = {
  virtual_scrolling: true, // 大列表虚拟滚动
  lazy_loading: true,      // 图片懒加载
  code_splitting: true,    // 路由代码分割
  web_workers: true,       // 复杂计算后台处理
  canvas_optimization: true, // Canvas离屏渲染
  memory_management: {
    max_cache: '500MB',
    lru_strategy: true,
    garbage_collection: 'manual'
  }
};
```

### 7.2 网络优化
- HTTP/2 服务器推送
- 资源预加载策略
- CDN 静态资源分发
- 请求防抖与节流
- 离线缓存策略

## 八、国际化与本地化

### 8.1 多语言支持
```typescript
interface Localization {
  languages: ['zh-CN', 'en-US', 'ja-JP'];
  translation_keys: {
    ui_elements: string;
    psychology_terms: string;
    test_questions: string;
    error_messages: string;
  };
  rtl_support: boolean;
  number_formatting: LocaleString;
  date_formatting: LocaleString;
}
```

### 8.2 文化适配
- 心理测试题目本地化
- 文化相关心理学概念解释
- 节日与季节性内容调整
- 本地化心理健康资源

## 九、质量保证与测试

### 9.1 测试策略
```javascript
const testingStrategy = {
  unit_tests: 'Jest + Testing Library',
  integration_tests: 'Cypress',
  e2e_tests: 'Playwright',
  performance_tests: 'Lighthouse CI',
  accessibility_tests: 'axe-core',
  cross_platform_tests: 'GitHub Actions Matrix'
};
```

### 9.2 性能基准
- 冷启动时间: < 1.5s
- 热启动时间: < 0.5s
- 内存占用: 基础 < 300MB, 峰值 < 800MB
- CPU使用率: 空闲 < 5%, 高负载 < 30%
- 动画帧率: 稳定 60fps (4K下降至30fps可接受)

## 十、部署与分发

### 10.1 打包配置
```javascript
const buildConfig = {
  electron: {
    platforms: ['win32', 'darwin', 'linux'],
    architectures: ['x64', 'arm64'],
    compression: 'maximum',
    code_signing: true
  },
  
  installers: {
    windows: 'NSIS + MSI',
    macos: 'DMG + PKG',
    linux: 'AppImage + DEB + RPM'
  },
  
  auto_updater: {
    strategy: 'differential',
    rollback_support: true,
    staged_rollout: true
  }
};
```

### 10.2 商业化功能
- 企业版用户管理
- 数据分析报告导出
- 批量心理测试管理
- API集成支持
- 白标解决方案

## 十一、维护与扩展

### 11.1 插件系统设计
```typescript
interface PluginSystem {
  core_apis: PluginAPI;
  theme_engine: ThemePlugin;
  widget_system: WidgetPlugin;
  test_modules: TestPlugin;
  data_exporters: ExportPlugin;
}
```

### 11.2 未来扩展方向
- AI心理咨询助手
- VR/AR心理治疗场景
- 生物反馈硬件集成
- 区块链隐私保护
- 脑机接口实验支持

## 十二、项目交付标准

### 12.1 功能完整性检查清单
- [ ] 所有界面图标统一且正常显示
- [ ] 心理笔记功能完整可用
- [ ] 心理测试结果分析正常 (雷达图+柱状图)
- [ ] 心理相册图片正常加载显示
- [ ] 百宝箱所有子功能正常使用
- [ ] 虚拟仿真3D场景正常运行
- [ ] GIS地理分析功能正常
- [ ] 音乐播放器功能完整 (除百宝箱外其他界面删除)
- [ ] 视频背景正常播放 (Wallpaper.mp4)
- [ ] 双语言切换功能正常
- [ ] 亮色/暗色主题切换正常
- [ ] 登录注册界面图标显示
- [ ] 用户头像切换功能正常
- [ ] 个人信息设置交互正常
- [ ] 通知设置功能可用
- [ ] 开机自启动功能可用
- [ ] 所有API调用正常
- [ ] 界面过渡动画丝滑
- [ ] 跨平台兼容性测试通过

### 12.2 性能验收标准
- 应用启动时间 ≤ 1.5秒
- 界面切换延迟 ≤ 300毫秒
- 内存使用稳定在500MB以下
- CPU空闲使用率 ≤ 5%
- 所有动画保持60fps
- 网络请求响应时间 ≤ 2秒
- 离线功能支持72小时使用

### 12.3 兼容性验收
- Windows 7/8/10/11 全版本支持
- macOS 10.15+ 完整支持  
- Linux (Ubuntu/Debian/Deepin) 正常运行
- 4K/2K/1080p 分辨率适配
- 触屏设备交互支持
- 高DPI显示器优化

通过以上完善的需求文档，确保项目能够满足所有功能要求，提供稳定、高性能、用户友好的跨平台心理分析应用程序。