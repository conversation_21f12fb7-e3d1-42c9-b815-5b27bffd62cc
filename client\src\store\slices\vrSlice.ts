import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { VRScene, VRSession } from '../../shared/types';

interface VRState {
  scenes: VRScene[];
  currentScene: VRScene | null;
  sessions: VRSession[];
  currentSession: VRSession | null;
  isVRSupported: boolean;
  isVRActive: boolean;
  isLoading: boolean;
  error: string | null;
  settings: {
    comfort: 'comfortable' | 'moderate' | 'intense';
    locomotion: 'teleport' | 'smooth' | 'room-scale';
    handTracking: boolean;
    hapticFeedback: boolean;
  };
}

const initialState: VRState = {
  scenes: [],
  currentScene: null,
  sessions: [],
  currentSession: null,
  isVRSupported: false,
  isVRActive: false,
  isLoading: false,
  error: null,
  settings: {
    comfort: 'comfortable',
    locomotion: 'teleport',
    handTracking: false,
    hapticFeedback: true,
  },
};

const vrSlice = createSlice({
  name: 'vr',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setVRSupported: (state, action: PayloadAction<boolean>) => {
      state.isVRSupported = action.payload;
    },
    setVRActive: (state, action: PayloadAction<boolean>) => {
      state.isVRActive = action.payload;
    },
    setScenes: (state, action: PayloadAction<VRScene[]>) => {
      state.scenes = action.payload;
    },
    setCurrentScene: (state, action: PayloadAction<VRScene | null>) => {
      state.currentScene = action.payload;
    },
    setSessions: (state, action: PayloadAction<VRSession[]>) => {
      state.sessions = action.payload;
    },
    addSession: (state, action: PayloadAction<VRSession>) => {
      state.sessions.unshift(action.payload);
    },
    setCurrentSession: (state, action: PayloadAction<VRSession | null>) => {
      state.currentSession = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<VRState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
  },
});

export const {
  setLoading,
  setError,
  setVRSupported,
  setVRActive,
  setScenes,
  setCurrentScene,
  setSessions,
  addSession,
  setCurrentSession,
  updateSettings,
} = vrSlice.actions;

export default vrSlice.reducer;
