#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 LENGSHANG 项目初始化脚本');
console.log('================================');

// 检查Node.js版本
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  console.log(`📋 检查Node.js版本: ${nodeVersion}`);
  
  if (majorVersion < 16) {
    console.error('❌ 错误: 需要Node.js 16或更高版本');
    process.exit(1);
  }
  
  console.log('✅ Node.js版本检查通过');
}

// 检查包管理器
function detectPackageManager() {
  console.log('📦 检测包管理器...');
  
  if (fs.existsSync('pnpm-lock.yaml')) {
    console.log('✅ 检测到 pnpm');
    return 'pnpm';
  }
  
  if (fs.existsSync('yarn.lock')) {
    console.log('✅ 检测到 yarn');
    return 'yarn';
  }
  
  console.log('✅ 使用 npm');
  return 'npm';
}

// 安装依赖
function installDependencies(packageManager) {
  console.log('📥 安装依赖包...');
  
  try {
    const installCommand = packageManager === 'npm' ? 'npm install' : `${packageManager} install`;
    
    console.log(`执行: ${installCommand}`);
    execSync(installCommand, { stdio: 'inherit', cwd: process.cwd() });
    
    console.log('✅ 根目录依赖安装完成');
    
    // 安装客户端依赖
    console.log('📥 安装客户端依赖...');
    execSync(installCommand, { stdio: 'inherit', cwd: path.join(process.cwd(), 'client') });
    console.log('✅ 客户端依赖安装完成');
    
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message);
    process.exit(1);
  }
}

// 创建环境配置文件
function createEnvFiles() {
  console.log('⚙️ 创建环境配置文件...');
  
  const clientEnvPath = path.join(process.cwd(), 'client', '.env');
  const clientEnvContent = `# LENGSHANG 客户端环境配置
REACT_APP_NAME=LENGSHANG
REACT_APP_VERSION=1.0.0
REACT_APP_API_URL=http://localhost:3001
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_ERROR_REPORTING=true

# 开发模式配置
GENERATE_SOURCEMAP=true
FAST_REFRESH=true

# 构建配置
BUILD_PATH=build
PUBLIC_URL=./
`;

  if (!fs.existsSync(clientEnvPath)) {
    fs.writeFileSync(clientEnvPath, clientEnvContent);
    console.log('✅ 创建客户端 .env 文件');
  } else {
    console.log('ℹ️ 客户端 .env 文件已存在');
  }
}

// 创建开发脚本
function createDevScripts() {
  console.log('📝 创建开发脚本...');
  
  const scriptsDir = path.join(process.cwd(), 'scripts');
  if (!fs.existsSync(scriptsDir)) {
    fs.mkdirSync(scriptsDir, { recursive: true });
  }
  
  // 创建启动脚本
  const startScript = `#!/bin/bash
echo "🚀 启动 LENGSHANG 开发环境"
echo "=========================="

# 检查端口是否被占用
check_port() {
  if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️ 端口 $1 已被占用"
    return 1
  fi
  return 0
}

# 启动客户端
start_client() {
  echo "📱 启动客户端应用..."
  cd client
  npm start &
  CLIENT_PID=$!
  echo "客户端 PID: $CLIENT_PID"
  cd ..
}

# 清理函数
cleanup() {
  echo "🧹 清理进程..."
  if [ ! -z "$CLIENT_PID" ]; then
    kill $CLIENT_PID 2>/dev/null
  fi
  exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 检查端口
if ! check_port 3000; then
  echo "❌ 端口 3000 被占用，请先关闭占用该端口的程序"
  exit 1
fi

# 启动服务
start_client

echo "✅ 开发环境启动完成"
echo "📱 客户端: http://localhost:3000"
echo "按 Ctrl+C 停止所有服务"

# 等待
wait
`;

  const startScriptPath = path.join(scriptsDir, 'start-dev.sh');
  fs.writeFileSync(startScriptPath, startScript);
  
  // 设置执行权限 (Unix系统)
  if (process.platform !== 'win32') {
    try {
      execSync(`chmod +x ${startScriptPath}`);
    } catch (error) {
      console.warn('⚠️ 无法设置脚本执行权限');
    }
  }
  
  console.log('✅ 创建开发启动脚本');
}

// 创建构建脚本
function createBuildScripts() {
  const scriptsDir = path.join(process.cwd(), 'scripts');
  
  const buildScript = `#!/bin/bash
echo "🔨 构建 LENGSHANG 应用"
echo "===================="

# 构建客户端
echo "📱 构建客户端应用..."
cd client
npm run build
if [ $? -eq 0 ]; then
  echo "✅ 客户端构建完成"
else
  echo "❌ 客户端构建失败"
  exit 1
fi
cd ..

# 构建Electron应用
echo "💻 构建Electron应用..."
cd client
npm run build:electron
if [ $? -eq 0 ]; then
  echo "✅ Electron应用构建完成"
else
  echo "❌ Electron应用构建失败"
  exit 1
fi
cd ..

echo "🎉 所有构建任务完成"
`;

  const buildScriptPath = path.join(scriptsDir, 'build.sh');
  fs.writeFileSync(buildScriptPath, buildScript);
  
  if (process.platform !== 'win32') {
    try {
      execSync(`chmod +x ${buildScriptPath}`);
    } catch (error) {
      console.warn('⚠️ 无法设置脚本执行权限');
    }
  }
  
  console.log('✅ 创建构建脚本');
}

// 显示完成信息
function showCompletionInfo() {
  console.log('\n🎉 项目初始化完成！');
  console.log('==================');
  console.log('\n📚 快速开始:');
  console.log('1. 启动开发环境:');
  console.log('   npm run dev');
  console.log('\n2. 构建应用:');
  console.log('   npm run build');
  console.log('\n3. 启动Electron应用:');
  console.log('   cd client && npm run electron:dev');
  console.log('\n📖 更多信息请查看 README.md');
  console.log('\n🌟 祝您开发愉快！');
}

// 主函数
function main() {
  try {
    checkNodeVersion();
    const packageManager = detectPackageManager();
    installDependencies(packageManager);
    createEnvFiles();
    createDevScripts();
    createBuildScripts();
    showCompletionInfo();
  } catch (error) {
    console.error('❌ 初始化失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  checkNodeVersion,
  detectPackageManager,
  installDependencies,
  createEnvFiles,
  createDevScripts,
  createBuildScripts,
};
