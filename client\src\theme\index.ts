import { createTheme, ThemeOptions } from '@mui/material/styles';
import { THEMES } from '../../shared/constants';

// 扩展主题类型
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      gradients: {
        primary: string;
        secondary: string;
        accent: string;
      };
      shadows: {
        glass: string;
        elevated: string;
      };
      animations: {
        transition: string;
        hover: string;
      };
    };
  }

  interface ThemeOptions {
    custom?: {
      gradients?: {
        primary?: string;
        secondary?: string;
        accent?: string;
      };
      shadows?: {
        glass?: string;
        elevated?: string;
      };
      animations?: {
        transition?: string;
        hover?: string;
      };
    };
  }
}

// 基础主题配置
const baseTheme: ThemeOptions = {
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      '"PingFang SC"',
      '"Hiragino Sans GB"',
      '"Microsoft YaHei"',
      '"Helvetica Neue"',
      'Helvetica',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 600,
          padding: '8px 16px',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 30px rgba(0,0,0,0.15)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
            '&.Mui-focused': {
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            },
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 16,
          boxShadow: '0 20px 60px rgba(0,0,0,0.3)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          fontWeight: 500,
        },
      },
    },
  },
};

// 亮色主题
const lightTheme = createTheme({
  ...baseTheme,
  palette: {
    mode: 'light',
    primary: {
      main: THEMES.light.primary,
      light: '#B8E6FF',
      dark: '#5BA3D0',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: THEMES.light.secondary,
      light: '#FFD1DC',
      dark: '#E6A4B4',
      contrastText: '#FFFFFF',
    },
    background: {
      default: THEMES.light.background,
      paper: THEMES.light.surface,
    },
    text: {
      primary: THEMES.light.text,
      secondary: '#708090',
    },
    divider: THEMES.light.border,
    success: {
      main: THEMES.light.accent,
    },
  },
  custom: {
    gradients: {
      primary: THEMES.light.primaryGradient,
      secondary: 'linear-gradient(135deg, #FFB6C1 0%, #FFF0F5 100%)',
      accent: 'linear-gradient(135deg, #98FB98 0%, #F0FFF0 100%)',
    },
    shadows: {
      glass: '0 8px 32px rgba(31, 38, 135, 0.37)',
      elevated: '0 8px 25px rgba(0,0,0,0.15)',
    },
    animations: {
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      hover: 'transform 0.2s ease-in-out',
    },
  },
});

// 暗色主题
const darkTheme = createTheme({
  ...baseTheme,
  palette: {
    mode: 'dark',
    primary: {
      main: THEMES.dark.secondary,
      light: '#FFB3D1',
      dark: '#D6527A',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: THEMES.dark.accent,
      light: '#C9F2C7',
      dark: '#7BC97B',
      contrastText: '#000000',
    },
    background: {
      default: THEMES.dark.background,
      paper: THEMES.dark.surface,
    },
    text: {
      primary: THEMES.dark.text,
      secondary: '#A6ADC8',
    },
    divider: THEMES.dark.border,
    success: {
      main: THEMES.dark.accent,
    },
  },
  custom: {
    gradients: {
      primary: THEMES.dark.primaryGradient,
      secondary: 'linear-gradient(135deg, #F38BA8 0%, #1E1E2E 100%)',
      accent: 'linear-gradient(135deg, #A6E3A1 0%, #1E1E2E 100%)',
    },
    shadows: {
      glass: '0 8px 32px rgba(0, 0, 0, 0.5)',
      elevated: '0 8px 25px rgba(0,0,0,0.3)',
    },
    animations: {
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      hover: 'transform 0.2s ease-in-out',
    },
  },
});

// 主题选择器
export const getTheme = (mode: 'light' | 'dark' | 'auto') => {
  if (mode === 'auto') {
    // 检测系统主题
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    return prefersDark ? darkTheme : lightTheme;
  }
  
  return mode === 'dark' ? darkTheme : lightTheme;
};

// 默认导出亮色主题
export const theme = lightTheme;

// 导出所有主题
export { lightTheme, darkTheme };

// 主题工具函数
export const createCustomTheme = (customOptions: ThemeOptions) => {
  return createTheme({
    ...baseTheme,
    ...customOptions,
  });
};

// 响应式断点
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
};

// 常用的样式工具
export const styleUtils = {
  glassmorphism: (theme: any) => ({
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: theme.shape.borderRadius,
  }),
  
  cardHover: {
    transition: 'all 0.3s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
    },
  },
  
  gradientText: (gradient: string) => ({
    background: gradient,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
  }),
  
  centerFlex: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  absoluteCenter: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  },
};
