import Dexie, { Table } from 'dexie';
import { PsychologyNote, TestResult, MediaFile, VRSession, EmotionHeatMapData } from '@shared/types';

// 数据库接口
export interface AppDatabase extends Dexie {
  notes: Table<PsychologyNote>;
  testResults: Table<TestResult>;
  mediaFiles: Table<MediaFile>;
  vrSessions: Table<VRSession>;
  emotionData: Table<EmotionHeatMapData>;
  settings: Table<{ key: string; value: any }>;
}

// 创建数据库实例
export const db = new Dexie('LengshangDB') as AppDatabase;

// 定义数据库结构
db.version(1).stores({
  notes: '++id, title, userId, createdAt, updatedAt, mood, tags',
  testResults: '++id, testId, userId, completedAt, scores',
  mediaFiles: '++id, filename, userId, uploadedAt, mimeType, size',
  vrSessions: '++id, sceneId, userId, startTime, endTime, duration',
  emotionData: '++id, location, emotion, timestamp, userId',
  settings: '++key, value',
});

// 数据库初始化
export const initializeDatabase = async () => {
  try {
    await db.open();
    console.log('Database initialized successfully');
    
    // 检查是否是首次运行
    const isFirstRun = await db.settings.get('isFirstRun');
    if (!isFirstRun) {
      await initializeDefaultData();
      await db.settings.put({ key: 'isFirstRun', value: false });
    }
    
    return true;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

// 初始化默认数据
const initializeDefaultData = async () => {
  try {
    // 添加默认设置
    const defaultSettings = [
      { key: 'theme', value: 'light' },
      { key: 'language', value: 'zh-CN' },
      { key: 'animations', value: true },
      { key: 'notifications', value: true },
    ];
    
    await db.settings.bulkPut(defaultSettings);
    console.log('Default data initialized');
  } catch (error) {
    console.error('Failed to initialize default data:', error);
  }
};

// 数据库操作类
export class DatabaseService {
  // 笔记操作
  static async createNote(note: Omit<PsychologyNote, 'id'>): Promise<string> {
    const id = await db.notes.add(note as PsychologyNote);
    return id.toString();
  }

  static async updateNote(id: string, updates: Partial<PsychologyNote>): Promise<void> {
    await db.notes.update(id, { ...updates, updatedAt: new Date() });
  }

  static async deleteNote(id: string): Promise<void> {
    await db.notes.delete(id);
  }

  static async getNotes(userId: string, limit?: number): Promise<PsychologyNote[]> {
    let query = db.notes.where('userId').equals(userId).orderBy('createdAt').reverse();
    if (limit) {
      query = query.limit(limit);
    }
    return await query.toArray();
  }

  static async searchNotes(userId: string, searchTerm: string): Promise<PsychologyNote[]> {
    return await db.notes
      .where('userId')
      .equals(userId)
      .filter(note => 
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.content.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .toArray();
  }

  // 测试结果操作
  static async saveTestResult(result: Omit<TestResult, 'id'>): Promise<string> {
    const id = await db.testResults.add(result as TestResult);
    return id.toString();
  }

  static async getTestResults(userId: string, testId?: string): Promise<TestResult[]> {
    let query = db.testResults.where('userId').equals(userId);
    if (testId) {
      query = query.and(result => result.testId === testId);
    }
    return await query.orderBy('completedAt').reverse().toArray();
  }

  // 媒体文件操作
  static async saveMediaFile(file: Omit<MediaFile, 'id'>): Promise<string> {
    const id = await db.mediaFiles.add(file as MediaFile);
    return id.toString();
  }

  static async getMediaFiles(userId: string): Promise<MediaFile[]> {
    return await db.mediaFiles
      .where('userId')
      .equals(userId)
      .orderBy('uploadedAt')
      .reverse()
      .toArray();
  }

  static async deleteMediaFile(id: string): Promise<void> {
    await db.mediaFiles.delete(id);
  }

  // VR会话操作
  static async saveVRSession(session: Omit<VRSession, 'id'>): Promise<string> {
    const id = await db.vrSessions.add(session as VRSession);
    return id.toString();
  }

  static async getVRSessions(userId: string): Promise<VRSession[]> {
    return await db.vrSessions
      .where('userId')
      .equals(userId)
      .orderBy('startTime')
      .reverse()
      .toArray();
  }

  // 情绪数据操作
  static async saveEmotionData(data: Omit<EmotionHeatMapData, 'id'>): Promise<string> {
    const id = await db.emotionData.add(data as EmotionHeatMapData);
    return id.toString();
  }

  static async getEmotionData(userId: string, startDate?: Date, endDate?: Date): Promise<EmotionHeatMapData[]> {
    let query = db.emotionData.where('userId').equals(userId);
    
    if (startDate && endDate) {
      query = query.and(data => data.timestamp >= startDate && data.timestamp <= endDate);
    }
    
    return await query.orderBy('timestamp').reverse().toArray();
  }

  // 设置操作
  static async getSetting(key: string): Promise<any> {
    const setting = await db.settings.get(key);
    return setting?.value;
  }

  static async setSetting(key: string, value: any): Promise<void> {
    await db.settings.put({ key, value });
  }

  // 数据统计
  static async getStats(userId: string) {
    const [notesCount, testResultsCount, mediaFilesCount, vrSessionsCount] = await Promise.all([
      db.notes.where('userId').equals(userId).count(),
      db.testResults.where('userId').equals(userId).count(),
      db.mediaFiles.where('userId').equals(userId).count(),
      db.vrSessions.where('userId').equals(userId).count(),
    ]);

    // 计算平均心情
    const notes = await db.notes.where('userId').equals(userId).toArray();
    const averageMood = notes.length > 0 
      ? notes.reduce((sum, note) => sum + note.mood, 0) / notes.length 
      : 0;

    return {
      notesCount,
      testResultsCount,
      mediaFilesCount,
      vrSessionsCount,
      averageMood: Math.round(averageMood * 10) / 10,
    };
  }

  // 数据导出
  static async exportData(userId: string) {
    const [notes, testResults, mediaFiles, vrSessions, emotionData] = await Promise.all([
      db.notes.where('userId').equals(userId).toArray(),
      db.testResults.where('userId').equals(userId).toArray(),
      db.mediaFiles.where('userId').equals(userId).toArray(),
      db.vrSessions.where('userId').equals(userId).toArray(),
      db.emotionData.where('userId').equals(userId).toArray(),
    ]);

    return {
      notes,
      testResults,
      mediaFiles,
      vrSessions,
      emotionData,
      exportDate: new Date(),
      version: '1.0.0',
    };
  }

  // 数据清理
  static async clearUserData(userId: string): Promise<void> {
    await Promise.all([
      db.notes.where('userId').equals(userId).delete(),
      db.testResults.where('userId').equals(userId).delete(),
      db.mediaFiles.where('userId').equals(userId).delete(),
      db.vrSessions.where('userId').equals(userId).delete(),
      db.emotionData.where('userId').equals(userId).delete(),
    ]);
  }
}

export default db;
