import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ToolboxState {
  activeTools: string[];
  musicPlayer: {
    isPlaying: boolean;
    currentTrack: any;
    playlist: any[];
    volume: number;
    repeat: boolean;
    shuffle: boolean;
  };
  todoList: {
    items: any[];
    filter: 'all' | 'active' | 'completed';
  };
  habits: {
    items: any[];
    streaks: Record<string, number>;
  };
  breathingExercise: {
    isActive: boolean;
    type: string;
    duration: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: ToolboxState = {
  activeTools: [],
  musicPlayer: {
    isPlaying: false,
    currentTrack: null,
    playlist: [],
    volume: 0.7,
    repeat: false,
    shuffle: false,
  },
  todoList: {
    items: [],
    filter: 'all',
  },
  habits: {
    items: [],
    streaks: {},
  },
  breathingExercise: {
    isActive: false,
    type: 'box',
    duration: 300,
  },
  isLoading: false,
  error: null,
};

const toolboxSlice = createSlice({
  name: 'toolbox',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setActiveTools: (state, action: PayloadAction<string[]>) => {
      state.activeTools = action.payload;
    },
    updateMusicPlayer: (state, action: PayloadAction<Partial<ToolboxState['musicPlayer']>>) => {
      state.musicPlayer = { ...state.musicPlayer, ...action.payload };
    },
    updateTodoList: (state, action: PayloadAction<Partial<ToolboxState['todoList']>>) => {
      state.todoList = { ...state.todoList, ...action.payload };
    },
    updateHabits: (state, action: PayloadAction<Partial<ToolboxState['habits']>>) => {
      state.habits = { ...state.habits, ...action.payload };
    },
    updateBreathingExercise: (state, action: PayloadAction<Partial<ToolboxState['breathingExercise']>>) => {
      state.breathingExercise = { ...state.breathingExercise, ...action.payload };
    },
  },
});

export const {
  setLoading,
  setError,
  setActiveTools,
  updateMusicPlayer,
  updateTodoList,
  updateHabits,
  updateBreathingExercise,
} = toolboxSlice.actions;

export default toolboxSlice.reducer;
