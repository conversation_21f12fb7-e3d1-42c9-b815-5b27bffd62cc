// 共享类型定义

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: 'zh-CN' | 'en-US' | 'ja-JP';
  theme: 'light' | 'dark';
  animations: boolean;
  notifications: boolean;
  autoStart: boolean;
}

// 心理笔记相关类型
export interface PsychologyNote {
  id: string;
  title: string;
  content: string; // Markdown格式
  emotion: EmotionTag[];
  mood: number; // 1-10心情指数
  weather?: WeatherInfo;
  location?: GeoLocation;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  attachments: MediaFile[];
  userId: string;
}

export interface EmotionTag {
  id: string;
  name: string;
  color: string;
  intensity: number; // 1-10强度
}

export interface WeatherInfo {
  temperature: number;
  humidity: number;
  condition: string;
  location: string;
  timestamp: Date;
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

export interface MediaFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: Date;
}

// 心理测试相关类型
export interface PsychologyTest {
  id: string;
  name: string;
  description: string;
  type: TestType;
  questions: TestQuestion[];
  scoringRules: ScoringRule[];
  resultTemplates: ResultTemplate[];
  version: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type TestType = 'MBTI' | 'BIG_FIVE' | 'HOLLAND' | 'SDS' | 'SAS' | 'PSS';

export interface TestQuestion {
  id: string;
  text: string;
  options: TestOption[];
  category?: string;
  weight?: number;
}

export interface TestOption {
  id: string;
  text: string;
  score: number;
  dimension?: string;
}

export interface ScoringRule {
  dimension: string;
  calculation: string;
  maxScore: number;
  minScore: number;
}

export interface ResultTemplate {
  scoreRange: {
    min: number;
    max: number;
  };
  dimension?: string;
  title: string;
  description: string;
  recommendations?: string[];
}

export interface TestResult {
  id: string;
  testId: string;
  userId: string;
  answers: TestAnswer[];
  scores: TestScore[];
  completedAt: Date;
  timeSpent: number; // 秒
}

export interface TestAnswer {
  questionId: string;
  optionId: string;
  answeredAt: Date;
}

export interface TestScore {
  dimension: string;
  score: number;
  percentage: number;
  interpretation: string;
}

// VR仿真相关类型
export interface VRScene {
  id: string;
  name: string;
  description: string;
  type: VRSceneType;
  environment: string;
  duration: number; // 分钟
  difficulty: number; // 1-5
  settings: VRSceneSettings;
  isActive: boolean;
}

export type VRSceneType = 'meditation' | 'emotion_release' | 'memory_palace' | 'social_simulation' | 'fear_exposure';

export interface VRSceneSettings {
  audio?: string;
  lighting?: string;
  interactions?: string[];
  customizations?: Record<string, any>;
}

export interface VRSession {
  id: string;
  sceneId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  feedback?: VRFeedback;
}

export interface VRFeedback {
  rating: number; // 1-5
  comfort: number; // 1-5
  effectiveness: number; // 1-5
  comments?: string;
}

// 地理分析相关类型
export interface EmotionHeatMapData {
  location: GeoLocation;
  emotion: string;
  intensity: number;
  count: number;
  timestamp: Date;
}

export interface POI {
  id: string;
  name: string;
  category: string;
  location: GeoLocation;
  emotionTags: string[];
  rating?: number;
  description?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 事件类型
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: Date;
}

// 配置类型
export interface AppConfig {
  version: string;
  environment: 'development' | 'production' | 'test';
  api: {
    baseUrl: string;
    timeout: number;
  };
  features: {
    vr: boolean;
    gis: boolean;
    cloudSync: boolean;
  };
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
}

// 统计数据类型
export interface UsageStats {
  dailyActiveUsers: number;
  monthlyActiveUsers: number;
  totalNotes: number;
  totalTests: number;
  averageSessionTime: number;
  popularFeatures: string[];
}
