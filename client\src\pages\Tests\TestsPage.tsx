import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Box,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Paper,
  Divider,
} from '@mui/material';
import { motion } from 'framer-motion';

// 心理测试数据
const psychologyTests = [
  {
    id: 'mbti',
    title: 'MBTI人格测试',
    description: '了解你的人格类型和性格特征',
    duration: 15,
    questions: 60,
    category: '人格测试',
    color: '#9C27B0',
    icon: '🧠',
    questions_data: [
      {
        id: 1,
        question: '在聚会中，你更倾向于：',
        options: [
          { id: 'a', text: '与很多人交谈', type: 'E' },
          { id: 'b', text: '与少数几个人深入交谈', type: 'I' }
        ]
      },
      {
        id: 2,
        question: '你更相信：',
        options: [
          { id: 'a', text: '经验和实际情况', type: 'S' },
          { id: 'b', text: '直觉和可能性', type: 'N' }
        ]
      },
      {
        id: 3,
        question: '做决定时，你更依赖：',
        options: [
          { id: 'a', text: '逻辑分析', type: 'T' },
          { id: 'b', text: '个人价值观', type: 'F' }
        ]
      },
      {
        id: 4,
        question: '你更喜欢：',
        options: [
          { id: 'a', text: '有计划和组织', type: 'J' },
          { id: 'b', text: '灵活和自发', type: 'P' }
        ]
      },
      {
        id: 5,
        question: '你的能量来源主要是：',
        options: [
          { id: 'a', text: '与他人互动', type: 'E' },
          { id: 'b', text: '独处思考', type: 'I' }
        ]
      }
    ]
  },
  {
    id: 'depression',
    title: '抑郁自评量表',
    description: '评估当前的情绪状态和抑郁程度',
    duration: 10,
    questions: 20,
    category: '情绪评估',
    color: '#2196F3',
    icon: '💙',
    questions_data: [
      {
        id: 1,
        question: '我感到情绪低落、沮丧或绝望',
        options: [
          { id: 'a', text: '从不', score: 0 },
          { id: 'b', text: '偶尔', score: 1 },
          { id: 'c', text: '经常', score: 2 },
          { id: 'd', text: '总是', score: 3 }
        ]
      },
      {
        id: 2,
        question: '我对平时感兴趣的事情失去兴趣',
        options: [
          { id: 'a', text: '从不', score: 0 },
          { id: 'b', text: '偶尔', score: 1 },
          { id: 'c', text: '经常', score: 2 },
          { id: 'd', text: '总是', score: 3 }
        ]
      },
      {
        id: 3,
        question: '我感到疲倦或缺乏精力',
        options: [
          { id: 'a', text: '从不', score: 0 },
          { id: 'b', text: '偶尔', score: 1 },
          { id: 'c', text: '经常', score: 2 },
          { id: 'd', text: '总是', score: 3 }
        ]
      }
    ]
  },
  {
    id: 'anxiety',
    title: '焦虑自评量表',
    description: '评估焦虑水平和相关症状',
    duration: 8,
    questions: 15,
    category: '情绪评估',
    color: '#FF9800',
    icon: '😰',
    questions_data: [
      {
        id: 1,
        question: '我感到紧张或焦虑',
        options: [
          { id: 'a', text: '从不', score: 0 },
          { id: 'b', text: '偶尔', score: 1 },
          { id: 'c', text: '经常', score: 2 },
          { id: 'd', text: '总是', score: 3 }
        ]
      },
      {
        id: 2,
        question: '我担心可能发生的坏事',
        options: [
          { id: 'a', text: '从不', score: 0 },
          { id: 'b', text: '偶尔', score: 1 },
          { id: 'c', text: '经常', score: 2 },
          { id: 'd', text: '总是', score: 3 }
        ]
      }
    ]
  }
];

export const TestsPage: React.FC = () => {
  const [selectedTest, setSelectedTest] = useState<any>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const handleStartTest = (test: any) => {
    setSelectedTest(test);
    setCurrentQuestion(0);
    setAnswers({});
    setShowResults(false);
  };

  const handleAnswer = (questionId: number, answer: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
  };

  const handleNext = () => {
    if (currentQuestion < selectedTest.questions_data.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    } else {
      calculateResults();
    }
  };

  const calculateResults = () => {
    if (selectedTest.id === 'mbti') {
      const types = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
      
      Object.values(answers).forEach((answer: any) => {
        const question = selectedTest.questions_data.find((q: any) => q.id === parseInt(Object.keys(answers).find(key => answers[parseInt(key)] === answer) || '0'));
        const option = question?.options.find((opt: any) => opt.id === answer);
        if (option?.type) {
          types[option.type as keyof typeof types]++;
        }
      });

      const personality = 
        (types.E > types.I ? 'E' : 'I') +
        (types.S > types.N ? 'S' : 'N') +
        (types.T > types.F ? 'T' : 'F') +
        (types.J > types.P ? 'J' : 'P');

      const personalityTypes: Record<string, string> = {
        'INTJ': '建筑师 - 富有想象力和战略性的思想家',
        'INTP': '思想家 - 具有创新精神的发明家',
        'ENTJ': '指挥官 - 大胆、富有想象力的强势领导者',
        'ENTP': '辩论家 - 聪明好奇的思想家',
        'INFJ': '提倡者 - 安静而神秘的理想主义者',
        'INFP': '调停者 - 诗意、善良的利他主义者',
        'ENFJ': '主人公 - 富有魅力的鼓舞人心的领导者',
        'ENFP': '竞选者 - 热情、有创造力的社交家',
        'ISTJ': '物流师 - 实用主义的事实导向者',
        'ISFJ': '守护者 - 非常专注、温暖的守护者',
        'ESTJ': '总经理 - 出色的管理者',
        'ESFJ': '执政官 - 极有同情心、受欢迎的人',
        'ISTP': '鉴赏家 - 大胆而实际的实验者',
        'ISFP': '探险家 - 灵活有魅力的艺术家',
        'ESTP': '企业家 - 聪明、精力充沛的感知者',
        'ESFP': '娱乐家 - 自发的、精力充沛的娱乐者'
      };

      setTestResults({
        type: personality,
        description: personalityTypes[personality] || '未知类型',
        details: types
      });
    } else {
      // 计算分数类型的测试结果
      let totalScore = 0;
      Object.entries(answers).forEach(([questionId, answerId]) => {
        const question = selectedTest.questions_data.find((q: any) => q.id === parseInt(questionId));
        const option = question?.options.find((opt: any) => opt.id === answerId);
        if (option?.score !== undefined) {
          totalScore += option.score;
        }
      });

      let level = '';
      let description = '';
      
      if (selectedTest.id === 'depression') {
        if (totalScore <= 4) {
          level = '正常';
          description = '情绪状态良好，无明显抑郁症状';
        } else if (totalScore <= 9) {
          level = '轻度';
          description = '有轻微的抑郁情绪，建议关注心理健康';
        } else if (totalScore <= 14) {
          level = '中度';
          description = '存在明显的抑郁症状，建议寻求专业帮助';
        } else {
          level = '重度';
          description = '抑郁症状严重，强烈建议立即寻求专业心理治疗';
        }
      } else if (selectedTest.id === 'anxiety') {
        if (totalScore <= 3) {
          level = '正常';
          description = '焦虑水平正常，心理状态稳定';
        } else if (totalScore <= 7) {
          level = '轻度';
          description = '有轻微焦虑，可通过放松技巧缓解';
        } else if (totalScore <= 11) {
          level = '中度';
          description = '焦虑水平较高，建议学习应对技巧';
        } else {
          level = '重度';
          description = '严重焦虑，建议寻求专业心理帮助';
        }
      }

      setTestResults({
        score: totalScore,
        level,
        description
      });
    }
    
    setShowResults(true);
  };

  const handleCloseTest = () => {
    setSelectedTest(null);
    setCurrentQuestion(0);
    setAnswers({});
    setShowResults(false);
    setTestResults(null);
  };

  const progress = selectedTest ? ((currentQuestion + 1) / selectedTest.questions_data.length) * 100 : 0;

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 3 }}>
        心理测试
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        通过专业的心理测试了解自己的性格特征、情绪状态和心理健康水平
      </Typography>

      <Grid container spacing={3}>
        {psychologyTests.map((test, index) => (
          <Grid item xs={12} md={6} lg={4} key={test.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                  },
                }}
                onClick={() => handleStartTest(test)}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        width: 50,
                        height: 50,
                        borderRadius: '50%',
                        bgcolor: test.color,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1.5rem',
                        mr: 2,
                      }}
                    >
                      {test.icon}
                    </Box>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {test.title}
                      </Typography>
                      <Chip 
                        label={test.category} 
                        size="small" 
                        sx={{ bgcolor: `${test.color}20`, color: test.color }}
                      />
                    </Box>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {test.description}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="body2">
                      ⏱️ {test.duration} 分钟
                    </Typography>
                    <Typography variant="body2">
                      📝 {test.questions} 题
                    </Typography>
                  </Box>
                  
                  <Button
                    variant="contained"
                    fullWidth
                    sx={{
                      bgcolor: test.color,
                      '&:hover': { bgcolor: test.color, filter: 'brightness(0.9)' },
                    }}
                  >
                    开始测试
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* 测试对话框 */}
      <Dialog 
        open={!!selectedTest} 
        onClose={handleCloseTest}
        maxWidth="md"
        fullWidth
      >
        {selectedTest && !showResults && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="h6">{selectedTest.title}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {currentQuestion + 1} / {selectedTest.questions_data.length}
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={progress} 
                sx={{ mt: 1 }}
              />
            </DialogTitle>
            
            <DialogContent>
              {selectedTest.questions_data[currentQuestion] && (
                <Box>
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    {selectedTest.questions_data[currentQuestion].question}
                  </Typography>
                  
                  <FormControl component="fieldset">
                    <RadioGroup
                      value={answers[selectedTest.questions_data[currentQuestion].id] || ''}
                      onChange={(e) => handleAnswer(selectedTest.questions_data[currentQuestion].id, e.target.value)}
                    >
                      {selectedTest.questions_data[currentQuestion].options.map((option: any) => (
                        <FormControlLabel
                          key={option.id}
                          value={option.id}
                          control={<Radio />}
                          label={option.text}
                          sx={{ mb: 1 }}
                        />
                      ))}
                    </RadioGroup>
                  </FormControl>
                </Box>
              )}
            </DialogContent>
            
            <DialogActions>
              <Button onClick={handleCloseTest}>取消</Button>
              <Button 
                onClick={handleNext}
                variant="contained"
                disabled={!answers[selectedTest.questions_data[currentQuestion]?.id]}
              >
                {currentQuestion < selectedTest.questions_data.length - 1 ? '下一题' : '完成测试'}
              </Button>
            </DialogActions>
          </>
        )}

        {showResults && testResults && (
          <>
            <DialogTitle>
              <Typography variant="h6">测试结果</Typography>
            </DialogTitle>
            
            <DialogContent>
              <Paper sx={{ p: 3, textAlign: 'center' }}>
                {selectedTest.id === 'mbti' ? (
                  <Box>
                    <Typography variant="h3" sx={{ fontWeight: 'bold', color: selectedTest.color, mb: 2 }}>
                      {testResults.type}
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 3 }}>
                      {testResults.description}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Grid container spacing={2}>
                      {Object.entries(testResults.details).map(([key, value]) => (
                        <Grid item xs={3} key={key}>
                          <Typography variant="h6">{key}</Typography>
                          <Typography variant="body2">{value as number}</Typography>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                ) : (
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: selectedTest.color, mb: 2 }}>
                      {testResults.level}
                    </Typography>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      得分: {testResults.score}
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 3 }}>
                      {testResults.description}
                    </Typography>
                  </Box>
                )}
              </Paper>
            </DialogContent>
            
            <DialogActions>
              <Button onClick={handleCloseTest} variant="contained">
                完成
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};
