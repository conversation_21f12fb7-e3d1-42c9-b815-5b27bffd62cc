const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // 对话框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // 事件监听
  onProtocolHandler: (callback) => {
    ipcRenderer.on('protocol-handler', (event, url) => callback(url));
  },
  
  onOpenSettings: (callback) => {
    ipcRenderer.on('open-settings', () => callback());
  },
  
  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
  
  // 系统信息
  platform: process.platform,
  
  // 文件系统操作（安全的）
  selectFile: async (options = {}) => {
    const result = await ipcRenderer.invoke('show-open-dialog', {
      properties: ['openFile'],
      filters: options.filters || [
        { name: 'All Files', extensions: ['*'] }
      ],
      ...options
    });
    return result;
  },
  
  selectDirectory: async () => {
    const result = await ipcRenderer.invoke('show-open-dialog', {
      properties: ['openDirectory']
    });
    return result;
  },
  
  saveFile: async (options = {}) => {
    const result = await ipcRenderer.invoke('show-save-dialog', {
      filters: options.filters || [
        { name: 'All Files', extensions: ['*'] }
      ],
      ...options
    });
    return result;
  }
});

// 暴露Node.js环境检测
contextBridge.exposeInMainWorld('isElectron', true);

// 暴露开发模式检测
contextBridge.exposeInMainWorld('isDev', process.env.NODE_ENV === 'development');

// 暴露系统主题检测
contextBridge.exposeInMainWorld('systemTheme', {
  shouldUseDarkColors: () => {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  },
  
  onThemeChange: (callback) => {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        callback(e.matches ? 'dark' : 'light');
      });
    }
  }
});

// 性能监控API
contextBridge.exposeInMainWorld('performanceAPI', {
  getMemoryUsage: () => {
    if (process.memoryUsage) {
      return process.memoryUsage();
    }
    return null;
  },
  
  getCPUUsage: () => {
    if (process.cpuUsage) {
      return process.cpuUsage();
    }
    return null;
  }
});

// 安全的存储API（基于localStorage的封装）
contextBridge.exposeInMainWorld('secureStorage', {
  setItem: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Storage error:', error);
      return false;
    }
  },
  
  getItem: (key) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Storage error:', error);
      return null;
    }
  },
  
  removeItem: (key) => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Storage error:', error);
      return false;
    }
  },
  
  clear: () => {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('Storage error:', error);
      return false;
    }
  }
});

// 网络状态API
contextBridge.exposeInMainWorld('networkAPI', {
  isOnline: () => navigator.onLine,
  
  onOnline: (callback) => {
    window.addEventListener('online', callback);
  },
  
  onOffline: (callback) => {
    window.addEventListener('offline', callback);
  }
});

// 通知API
contextBridge.exposeInMainWorld('notificationAPI', {
  isSupported: () => 'Notification' in window,
  
  requestPermission: async () => {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  },
  
  show: (title, options = {}) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, options);
    }
    return null;
  }
});

// 剪贴板API
contextBridge.exposeInMainWorld('clipboardAPI', {
  writeText: async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.error('Clipboard error:', error);
      return false;
    }
  },
  
  readText: async () => {
    try {
      return await navigator.clipboard.readText();
    } catch (error) {
      console.error('Clipboard error:', error);
      return '';
    }
  }
});

// 地理位置API
contextBridge.exposeInMainWorld('geolocationAPI', {
  isSupported: () => 'geolocation' in navigator,
  
  getCurrentPosition: (options = {}) => {
    return new Promise((resolve, reject) => {
      if ('geolocation' in navigator) {
        navigator.geolocation.getCurrentPosition(resolve, reject, options);
      } else {
        reject(new Error('Geolocation not supported'));
      }
    });
  },
  
  watchPosition: (callback, errorCallback, options = {}) => {
    if ('geolocation' in navigator) {
      return navigator.geolocation.watchPosition(callback, errorCallback, options);
    }
    return null;
  },
  
  clearWatch: (watchId) => {
    if ('geolocation' in navigator && watchId) {
      navigator.geolocation.clearWatch(watchId);
    }
  }
});

// 媒体设备API
contextBridge.exposeInMainWorld('mediaAPI', {
  getUserMedia: async (constraints) => {
    try {
      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      console.error('Media error:', error);
      throw error;
    }
  },
  
  enumerateDevices: async () => {
    try {
      return await navigator.mediaDevices.enumerateDevices();
    } catch (error) {
      console.error('Media error:', error);
      return [];
    }
  }
});

// 错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// 开发模式下的调试工具
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('debugAPI', {
    log: (...args) => console.log('[Debug]', ...args),
    error: (...args) => console.error('[Debug]', ...args),
    warn: (...args) => console.warn('[Debug]', ...args),
    info: (...args) => console.info('[Debug]', ...args)
  });
}
