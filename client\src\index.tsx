import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import App from './App';
import { store } from './store';
import { theme } from './theme';
import { I18nProvider } from './i18n';
import { ErrorBoundary } from './components/ErrorBoundary';
import { ServiceWorkerProvider } from './services/ServiceWorkerProvider';

import './index.css';

// 性能监控
if (window.performanceAPI) {
  const memoryUsage = window.performanceAPI.getMemoryUsage();
  if (memoryUsage) {
    console.log('Initial memory usage:', memoryUsage);
  }
}

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // 这里可以添加错误上报逻辑
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // 这里可以添加错误上报逻辑
});

// 检测运行环境
const isElectron = window.isElectron || false;
const isDev = window.isDev || false;

console.log(`Running in ${isElectron ? 'Electron' : 'Browser'} mode`);
console.log(`Environment: ${isDev ? 'Development' : 'Production'}`);

// 创建根元素
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// 渲染应用
root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <BrowserRouter>
          <I18nProvider>
            <ThemeProvider theme={theme}>
              <CssBaseline />
              <ServiceWorkerProvider>
                <App />
              </ServiceWorkerProvider>
            </ThemeProvider>
          </I18nProvider>
        </BrowserRouter>
      </Provider>
    </ErrorBoundary>
  </React.StrictMode>
);

// 开发模式下的热重载
if (isDev && module.hot) {
  module.hot.accept('./App', () => {
    const NextApp = require('./App').default;
    root.render(
      <React.StrictMode>
        <ErrorBoundary>
          <Provider store={store}>
            <BrowserRouter>
              <I18nProvider>
                <ThemeProvider theme={theme}>
                  <CssBaseline />
                  <ServiceWorkerProvider>
                    <NextApp />
                  </ServiceWorkerProvider>
                </ThemeProvider>
              </I18nProvider>
            </BrowserRouter>
          </Provider>
        </ErrorBoundary>
      </React.StrictMode>
    );
  });
}

// 性能监控
if (isDev) {
  // 监控FPS
  let frames = 0;
  let lastTime = performance.now();
  
  function countFrames() {
    frames++;
    const currentTime = performance.now();
    
    if (currentTime >= lastTime + 1000) {
      const fps = Math.round((frames * 1000) / (currentTime - lastTime));
      console.log(`FPS: ${fps}`);
      frames = 0;
      lastTime = currentTime;
    }
    
    requestAnimationFrame(countFrames);
  }
  
  requestAnimationFrame(countFrames);
}

// 注册Service Worker（生产环境）
if (!isDev && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// 网络状态监控
if (window.networkAPI) {
  window.networkAPI.onOnline(() => {
    console.log('Network: Online');
    // 可以触发数据同步等操作
  });
  
  window.networkAPI.onOffline(() => {
    console.log('Network: Offline');
    // 可以显示离线提示
  });
}

// Electron特定的初始化
if (isElectron && window.electronAPI) {
  // 监听协议处理器
  window.electronAPI.onProtocolHandler((url: string) => {
    console.log('Protocol handler called with:', url);
    // 处理深层链接
    // 例如: lengshangapp://open/notes/123
  });
  
  // 监听设置页面打开
  window.electronAPI.onOpenSettings(() => {
    console.log('Open settings requested');
    // 导航到设置页面
  });
  
  // 获取应用版本
  window.electronAPI.getVersion().then((version: string) => {
    console.log('App version:', version);
  });
}

// 主题检测
if (window.systemTheme) {
  // 监听系统主题变化
  window.systemTheme.onThemeChange((theme: 'light' | 'dark') => {
    console.log('System theme changed to:', theme);
    // 可以自动切换应用主题
  });
}

// 清理函数
window.addEventListener('beforeunload', () => {
  // 清理资源
  if (isElectron && window.electronAPI) {
    window.electronAPI.removeAllListeners('protocol-handler');
    window.electronAPI.removeAllListeners('open-settings');
  }
});

// 开发模式下的调试信息
if (isDev) {
  console.log('=== LENGSHANG Psychology App ===');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('React version:', React.version);
  console.log('User agent:', navigator.userAgent);
  console.log('Screen resolution:', `${screen.width}x${screen.height}`);
  console.log('Viewport size:', `${window.innerWidth}x${window.innerHeight}`);
  console.log('Device pixel ratio:', window.devicePixelRatio);
  console.log('Language:', navigator.language);
  console.log('Platform:', navigator.platform);
  console.log('Online:', navigator.onLine);
  
  // 暴露调试API到全局
  (window as any).debugStore = store;
  (window as any).debugUtils = {
    getState: () => store.getState(),
    dispatch: store.dispatch,
  };
}
