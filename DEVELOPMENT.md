# LENGSHANG 开发指南

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 (推荐使用 pnpm 或 yarn)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/lengshang/psychology-app.git
cd psychology-app

# 运行初始化脚本
npm run setup

# 或手动安装
npm install
cd client && npm install
```

### 启动开发环境

```bash
# 启动Web版本
npm run dev

# 启动Electron版本
npm run dev:electron

# 启动Tauri版本 (需要Rust环境)
npm run dev:tauri
```

## 📁 项目结构

```
lengshang-psychology-app/
├── client/                 # 客户端应用
│   ├── public/            # 静态资源
│   │   ├── electron.js    # Electron主进程
│   │   ├── preload.js     # Electron预加载脚本
│   │   └── splash.html    # 启动画面
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── store/         # Redux状态管理
│   │   ├── services/      # 服务层
│   │   ├── theme/         # 主题配置
│   │   ├── i18n/          # 国际化
│   │   └── utils/         # 工具函数
│   └── package.json       # 客户端依赖
├── shared/                # 共享模块
│   ├── types/             # TypeScript类型定义
│   ├── constants/         # 常量定义
│   └── utils/             # 共享工具函数
├── scripts/               # 构建脚本
└── docs/                  # 文档
```

## 🛠️ 开发工具

### 代码规范

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 代码格式化
npm run format

# 类型检查
npm run type-check
```

### 测试

```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 构建

```bash
# 构建Web版本
npm run build

# 构建Electron应用
npm run build:electron

# 构建Tauri应用
npm run build:tauri
```

## 🎯 核心功能开发

### 1. 心理笔记模块

**位置**: `client/src/pages/Notes/`

**功能**:
- 创建、编辑、删除笔记
- 心情指数记录
- 情绪标签系统
- 位置和天气信息
- 搜索和筛选

**数据库**: IndexedDB (Dexie.js)

### 2. 心理测试模块

**位置**: `client/src/pages/Tests/`

**功能**:
- MBTI人格测试
- 大五人格测试
- 抑郁自评量表
- 焦虑自评量表
- 测试结果分析

### 3. 心理相册模块

**位置**: `client/src/pages/Gallery/`

**功能**:
- 图片上传和管理
- 情绪标记
- 相册分类
- 图片分析

### 4. 百宝箱模块

**位置**: `client/src/pages/Toolbox/`

**功能**:
- 音乐播放器
- 待办事项
- 习惯追踪
- 呼吸练习
- 冥想指导

### 5. VR虚拟仿真

**位置**: `client/src/pages/VR/`

**技术栈**: Three.js, WebXR

**功能**:
- 冥想花园场景
- 情绪释放空间
- 恐惧暴露治疗
- 社交模拟

### 6. GIS地理分析

**位置**: `client/src/pages/GIS/`

**技术栈**: Leaflet, D3.js

**功能**:
- 情绪热力图
- 地理位置分析
- 活动轨迹
- 兴趣点标记

## 🔧 技术栈详解

### 前端框架
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全
- **Material-UI**: 组件库
- **Framer Motion**: 动画库

### 状态管理
- **Redux Toolkit**: 状态管理
- **Redux Persist**: 状态持久化

### 数据存储
- **IndexedDB**: 本地数据库
- **Dexie.js**: IndexedDB封装

### 跨平台
- **Electron**: 桌面应用
- **Tauri**: 轻量级桌面应用

### 开发工具
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试
- **Storybook**: 组件开发

## 📱 平台特性

### Electron版本
- 原生菜单栏
- 系统托盘
- 文件系统访问
- 自动更新
- 深层链接

### Tauri版本
- 更小的包体积
- 更好的性能
- Rust后端
- 系统API访问

### Web版本
- PWA支持
- 离线功能
- 响应式设计
- 跨浏览器兼容

## 🌐 国际化

支持语言:
- 简体中文 (zh-CN)
- 英语 (en-US)
- 日语 (ja-JP)

添加新语言:
1. 在 `client/src/i18n/locales/` 添加语言文件
2. 更新 `client/src/i18n/index.tsx` 配置
3. 测试所有界面翻译

## 🎨 主题系统

支持主题:
- 亮色主题
- 暗色主题
- 自动切换

自定义主题:
1. 修改 `client/src/theme/index.ts`
2. 添加新的颜色配置
3. 更新组件样式

## 🔒 安全考虑

### Electron安全
- 禁用Node.js集成
- 启用上下文隔离
- 使用预加载脚本
- 验证外部URL

### 数据安全
- 本地数据加密
- 敏感信息脱敏
- 安全的API通信

## 📊 性能优化

### 代码分割
- 路由级别分割
- 组件懒加载
- 动态导入

### 内存管理
- 组件卸载清理
- 事件监听器移除
- 大文件处理优化

### 渲染优化
- React.memo使用
- useMemo和useCallback
- 虚拟滚动

## 🐛 调试技巧

### 开发工具
```bash
# 启用详细日志
DEBUG=* npm run dev

# Electron调试
npm run dev:electron -- --inspect

# React DevTools
# 浏览器扩展自动可用
```

### 常见问题
1. **Electron白屏**: 检查preload脚本路径
2. **热重载失败**: 清除缓存重启
3. **类型错误**: 运行 `npm run type-check`
4. **构建失败**: 检查依赖版本兼容性

## 📝 提交规范

使用 Conventional Commits:

```
feat: 添加心理测试模块
fix: 修复笔记保存问题
docs: 更新开发文档
style: 调整UI样式
refactor: 重构状态管理
test: 添加单元测试
chore: 更新依赖包
```

## 🚀 部署指南

### 开发环境
```bash
npm run dev
```

### 生产构建
```bash
npm run build
npm run start:prod
```

### 发布流程
1. 更新版本号
2. 运行完整测试
3. 构建所有平台版本
4. 创建发布标签
5. 上传到发布平台

## 📚 学习资源

- [React官方文档](https://react.dev/)
- [Electron文档](https://www.electronjs.org/docs)
- [Tauri文档](https://tauri.app/)
- [Material-UI文档](https://mui.com/)
- [Redux Toolkit文档](https://redux-toolkit.js.org/)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📞 技术支持

- 邮箱: <EMAIL>
- 文档: https://docs.lengshang.app
- 问题反馈: https://github.com/lengshang/psychology-app/issues
