{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["./shared/*"], "@client/*": ["./client/src/*"], "@admin/*": ["./admin-backend/src/*"], "@launcher/*": ["./web-launcher/src/*"]}}, "include": ["src/**/*", "shared/**/*", "client/src/**/*", "admin-backend/src/**/*", "web-launcher/src/**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.test.tsx"]}