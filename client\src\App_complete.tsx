import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { SnackbarProvider } from 'notistack';

import { store } from './store';
import { theme } from './theme';
import { MainLayout } from './components/Layout/MainLayout';

// 页面组件导入
import { HomePage } from './pages/HomePage';
import { NotesPage } from './pages/Notes/NotesPage';
import { TestsPage } from './pages/Tests/TestsPage';
import { GalleryPage } from './pages/Gallery/GalleryPage';
import { ToolboxPage } from './pages/Toolbox/ToolboxPage';
import { VRPage } from './pages/VR/VRPage';
import { GISPage } from './pages/GIS/GISPage';
import { SettingsPage } from './pages/Settings/SettingsPage';

import './App.css';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SnackbarProvider 
          maxSnack={3}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <Router>
            <Routes>
              <Route path="/" element={<MainLayout><HomePage /></MainLayout>} />
              <Route path="/notes" element={<MainLayout><NotesPage /></MainLayout>} />
              <Route path="/tests" element={<MainLayout><TestsPage /></MainLayout>} />
              <Route path="/gallery" element={<MainLayout><GalleryPage /></MainLayout>} />
              <Route path="/toolbox" element={<MainLayout><ToolboxPage /></MainLayout>} />
              <Route path="/vr" element={<MainLayout><VRPage /></MainLayout>} />
              <Route path="/gis" element={<MainLayout><GISPage /></MainLayout>} />
              <Route path="/settings" element={<MainLayout><SettingsPage /></MainLayout>} />
              <Route path="*" element={<MainLayout><HomePage /></MainLayout>} />
            </Routes>
          </Router>
        </SnackbarProvider>
      </ThemeProvider>
    </Provider>
  );
};

export default App;
