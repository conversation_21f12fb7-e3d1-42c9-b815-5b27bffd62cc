{"name": "lengshang-psychology-app", "version": "1.0.0", "description": "LENGSHANG 跨平台心理分析应用 - 一款融合前沿技术与心理学理论的全面心理健康工具", "main": "dist/main.js", "scripts": {"start": "node start.js", "setup": "node scripts/setup.js", "dev": "concurrently \"npm run dev:client\" \"npm run dev:shared\"", "dev:client": "cd client && npm run dev:react", "dev:shared": "nodemon --watch shared --ext ts,js,json --exec \"echo 'Shared files changed'\"", "dev:electron": "cd client && npm run dev", "dev:tauri": "cd client && npm run dev:tauri", "build": "npm run build:shared && npm run build:client", "build:shared": "tsc -p shared/tsconfig.json", "build:client": "cd client && npm run build", "build:electron": "cd client && npm run build:electron", "build:tauri": "cd client && npm run build:tauri", "test": "npm run test:shared && npm run test:client", "test:shared": "echo \"No tests for shared module yet\"", "test:client": "cd client && npm test", "test:watch": "cd client && npm test -- --watch", "test:coverage": "cd client && npm test -- --coverage --watchAll=false", "lint": "npm run lint:shared && npm run lint:client", "lint:shared": "eslint shared --ext .ts,.js", "lint:client": "cd client && npm run lint", "lint:fix": "npm run lint:shared -- --fix && npm run lint:client -- --fix", "type-check": "npm run type-check:shared && npm run type-check:client", "type-check:shared": "tsc -p shared/tsconfig.json --noEmit", "type-check:client": "cd client && npm run type-check", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "clean": "rimraf dist build client/build client/dist shared/dist", "clean:deps": "rimraf node_modules client/node_modules shared/node_modules", "reinstall": "npm run clean:deps && npm install && cd client && npm install", "release": "npm run build && npm run test && echo \"Ready for release\"", "start:prod": "cd client && npm run build && npm run electron:build", "electron:dev": "cd client && npm run electron:dev", "electron:build": "cd client && npm run electron:build", "tauri:dev": "cd client && npm run tauri:dev", "tauri:build": "cd client && npm run tauri:build"}, "keywords": ["psychology", "mental-health", "electron", "tauri", "react", "typescript", "cross-platform", "心理分析", "心理健康", "跨平台"], "author": "LENGSHANG Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/lengshang/psychology-app.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["client", "admin-backend", "web-launcher", "shared"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "concurrently": "^8.2.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "nodemon": "^3.0.1", "prettier": "^3.0.0", "rimraf": "^5.0.1", "typescript": "^5.1.6"}, "dependencies": {"cross-env": "^7.0.3"}}