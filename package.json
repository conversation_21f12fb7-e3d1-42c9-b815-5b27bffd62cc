{"name": "lengshang-psychology-app", "version": "1.0.0", "description": "LENGSHANG 跨平台心理分析应用 - 一款融合前沿技术与心理学理论的全面心理健康工具", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:admin\" \"npm run dev:launcher\"", "dev:client": "cd client && npm run dev", "dev:admin": "cd admin-backend && npm run dev", "dev:launcher": "cd web-launcher && npm run dev", "build": "npm run build:client && npm run build:admin && npm run build:launcher", "build:client": "cd client && npm run build", "build:admin": "cd admin-backend && npm run build", "build:launcher": "cd web-launcher && npm run build", "test": "npm run test:client && npm run test:admin", "test:client": "cd client && npm run test", "test:admin": "cd admin-backend && npm run test", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "setup": "npm install && npm run setup:client && npm run setup:admin && npm run setup:launcher", "setup:client": "cd client && npm install", "setup:admin": "cd admin-backend && npm install", "setup:launcher": "cd web-launcher && npm install", "electron:dev": "cd client && npm run electron:dev", "electron:build": "cd client && npm run electron:build", "tauri:dev": "cd client && npm run tauri:dev", "tauri:build": "cd client && npm run tauri:build"}, "keywords": ["psychology", "mental-health", "electron", "tauri", "react", "typescript", "cross-platform", "心理分析", "心理健康", "跨平台"], "author": "LENGSHANG Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/lengshang/psychology-app.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["client", "admin-backend", "web-launcher", "shared"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "dependencies": {"cross-env": "^7.0.3"}}