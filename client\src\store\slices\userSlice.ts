import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { User, UserPreferences } from '@shared/types';
import { DEFAULT_SETTINGS } from '@shared/constants';

// 用户状态接口
interface UserState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  profile: User | null;
  preferences: UserPreferences;
  token: string | null;
  refreshToken: string | null;
  lastLoginTime: Date | null;
  loginAttempts: number;
  isFirstTime: boolean;
}

// 初始状态
const initialState: UserState = {
  isAuthenticated: false,
  isLoading: false,
  error: null,
  profile: null,
  preferences: {
    language: DEFAULT_SETTINGS.language,
    theme: DEFAULT_SETTINGS.theme,
    animations: DEFAULT_SETTINGS.animations,
    notifications: DEFAULT_SETTINGS.notifications,
    autoStart: DEFAULT_SETTINGS.autoStart,
  },
  token: null,
  refreshToken: null,
  lastLoginTime: null,
  loginAttempts: 0,
  isFirstTime: true,
};

// 异步thunks
export const loginUser = createAsyncThunk(
  'user/login',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的登录API
      // 目前使用模拟数据
      const mockUser: User = {
        id: '1',
        username: credentials.email.split('@')[0],
        email: credentials.email,
        avatar: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {
          language: 'zh-CN',
          theme: 'light',
          animations: true,
          notifications: true,
          autoStart: false,
        },
      };

      const mockToken = 'mock-jwt-token';
      const mockRefreshToken = 'mock-refresh-token';

      return {
        user: mockUser,
        token: mockToken,
        refreshToken: mockRefreshToken,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'user/register',
  async (userData: { username: string; email: string; password: string }, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的注册API
      // 目前使用模拟数据
      const mockUser: User = {
        id: Date.now().toString(),
        username: userData.username,
        email: userData.email,
        avatar: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        preferences: {
          language: 'zh-CN',
          theme: 'light',
          animations: true,
          notifications: true,
          autoStart: false,
        },
      };

      const mockToken = 'mock-jwt-token';
      const mockRefreshToken = 'mock-refresh-token';

      return {
        user: mockUser,
        token: mockToken,
        refreshToken: mockRefreshToken,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Registration failed');
    }
  }
);

export const refreshUserToken = createAsyncThunk(
  'user/refreshToken',
  async (refreshToken: string, { rejectWithValue }) => {
    try {
      // 这里应该调用实际的刷新token API
      const newToken = 'new-mock-jwt-token';
      const newRefreshToken = 'new-mock-refresh-token';

      return {
        token: newToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Token refresh failed');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<User>, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { user: UserState };
      const currentUser = state.user.profile;
      
      if (!currentUser) {
        throw new Error('No user profile found');
      }

      // 这里应该调用实际的更新API
      const updatedUser: User = {
        ...currentUser,
        ...profileData,
        updatedAt: new Date(),
      };

      return updatedUser;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Profile update failed');
    }
  }
);

// 创建slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },

    // 登出
    logout: (state) => {
      state.isAuthenticated = false;
      state.profile = null;
      state.token = null;
      state.refreshToken = null;
      state.lastLoginTime = null;
      state.error = null;
    },

    // 更新偏好设置
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      };
      
      // 如果有用户资料，也更新用户资料中的偏好设置
      if (state.profile) {
        state.profile.preferences = state.preferences;
      }
    },

    // 加载用户偏好设置
    loadUserPreferences: (state) => {
      try {
        const savedPreferences = window.secureStorage?.getItem('user_preferences');
        if (savedPreferences) {
          state.preferences = {
            ...state.preferences,
            ...savedPreferences,
          };
        }
      } catch (error) {
        console.error('Failed to load user preferences:', error);
      }
    },

    // 保存用户偏好设置
    saveUserPreferences: (state) => {
      try {
        window.secureStorage?.setItem('user_preferences', state.preferences);
      } catch (error) {
        console.error('Failed to save user preferences:', error);
      }
    },

    // 增加登录尝试次数
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
    },

    // 重置登录尝试次数
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
    },

    // 设置首次使用状态
    setFirstTime: (state, action: PayloadAction<boolean>) => {
      state.isFirstTime = action.payload;
    },

    // 设置认证状态（用于自动登录）
    setAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload;
    },

    // 设置用户资料
    setProfile: (state, action: PayloadAction<User>) => {
      state.profile = action.payload;
      state.preferences = action.payload.preferences;
    },

    // 设置token
    setTokens: (state, action: PayloadAction<{ token: string; refreshToken: string }>) => {
      state.token = action.payload.token;
      state.refreshToken = action.payload.refreshToken;
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.profile = action.payload.user;
        state.preferences = action.payload.user.preferences;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.lastLoginTime = new Date();
        state.loginAttempts = 0;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
      });

    // 注册
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.profile = action.payload.user;
        state.preferences = action.payload.user.preferences;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.lastLoginTime = new Date();
        state.isFirstTime = false;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 刷新token
    builder
      .addCase(refreshUserToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
      })
      .addCase(refreshUserToken.rejected, (state) => {
        // Token刷新失败，需要重新登录
        state.isAuthenticated = false;
        state.profile = null;
        state.token = null;
        state.refreshToken = null;
      });

    // 更新用户资料
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.preferences = action.payload.preferences;
        state.error = null;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// 导出actions
export const {
  setLoading,
  setError,
  clearError,
  logout,
  updatePreferences,
  loadUserPreferences,
  saveUserPreferences,
  incrementLoginAttempts,
  resetLoginAttempts,
  setFirstTime,
  setAuthenticated,
  setProfile,
  setTokens,
} = userSlice.actions;

// 选择器
export const selectUser = (state: { user: UserState }) => state.user;
export const selectIsAuthenticated = (state: { user: UserState }) => state.user.isAuthenticated;
export const selectUserProfile = (state: { user: UserState }) => state.user.profile;
export const selectUserPreferences = (state: { user: UserState }) => state.user.preferences;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
export const selectLoginAttempts = (state: { user: UserState }) => state.user.loginAttempts;
export const selectIsFirstTime = (state: { user: UserState }) => state.user.isFirstTime;

// 导出reducer
export default userSlice.reducer;
