import React from 'react';
import { Box, Container, Typography, Link } from '@mui/material';
import { motion } from 'framer-motion';
import { BackgroundVideo } from '../Background/BackgroundVideo';
import { LanguageSwitcher } from '../../i18n';

interface AuthLayoutProps {
  children: React.ReactNode;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <Box sx={{ position: 'relative', minHeight: '100vh', overflow: 'hidden' }}>
      {/* 背景视频 */}
      <BackgroundVideo />
      
      {/* 顶部语言切换器 */}
      <Box
        sx={{
          position: 'absolute',
          top: 20,
          right: 20,
          zIndex: 10,
        }}
      >
        <LanguageSwitcher variant="select" />
      </Box>

      {/* 主要内容 */}
      <Container
        maxWidth="sm"
        sx={{
          position: 'relative',
          zIndex: 1,
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          py: 4,
        }}
      >
        {/* Logo和标题 */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <motion.div
              initial={{ scale: 0.5, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ 
                type: 'spring',
                stiffness: 200,
                damping: 20,
                delay: 0.2 
              }}
            >
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 3,
                  color: 'white',
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  boxShadow: '0 10px 30px rgba(135, 206, 235, 0.3)',
                }}
              >
                心
              </Box>
            </motion.div>
            
            <Typography
              variant="h3"
              sx={{
                fontWeight: 'bold',
                color: 'white',
                textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
                mb: 1,
                fontFamily: '"KaiTi", "楷体", serif',
              }}
            >
              LENGSHANG
            </Typography>
            
            <Typography
              variant="h6"
              sx={{
                color: 'rgba(255,255,255,0.9)',
                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                fontWeight: 300,
              }}
            >
              跨平台心理分析应用
            </Typography>
          </Box>
        </motion.div>

        {/* 认证表单 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {children}
        </motion.div>

        {/* 底部信息 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Typography
              variant="body2"
              sx={{
                color: 'rgba(255,255,255,0.7)',
                textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
                mb: 2,
              }}
            >
              让心理健康触手可及 💚
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3 }}>
              <Link
                href="https://lengshang.app"
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: 'rgba(255,255,255,0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    color: 'white',
                    textDecoration: 'underline',
                  },
                }}
              >
                官网
              </Link>
              
              <Link
                href="https://docs.lengshang.app"
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: 'rgba(255,255,255,0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    color: 'white',
                    textDecoration: 'underline',
                  },
                }}
              >
                帮助文档
              </Link>
              
              <Link
                href="mailto:<EMAIL>"
                sx={{
                  color: 'rgba(255,255,255,0.8)',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    color: 'white',
                    textDecoration: 'underline',
                  },
                }}
              >
                联系我们
              </Link>
            </Box>
          </Box>
        </motion.div>
      </Container>

      {/* 装饰性元素 */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: 100,
          height: 100,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          zIndex: 0,
        }}
      />
      
      <Box
        sx={{
          position: 'absolute',
          bottom: '15%',
          right: '8%',
          width: 150,
          height: 150,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(10px)',
          zIndex: 0,
        }}
      />
      
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          left: '2%',
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: 'rgba(152, 251, 152, 0.1)',
          backdropFilter: 'blur(10px)',
          zIndex: 0,
        }}
      />
    </Box>
  );
};
