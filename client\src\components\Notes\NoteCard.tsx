import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Box,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  LocationOn as LocationIcon,
  WbSunny as WeatherIcon,
  Schedule as TimeIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useDispatch } from 'react-redux';

import { PsychologyNote } from '../../shared/types';
import { DatabaseService } from '../../services/database';
import { addNotification } from '../../store/slices/appSlice';
import { dateUtils, stringUtils } from '../../shared/utils';

interface NoteCardProps {
  note: PsychologyNote;
  onEdit: () => void;
  onDelete: () => void;
}

export const NoteCard: React.FC<NoteCardProps> = ({ note, onEdit, onDelete }) => {
  const dispatch = useDispatch();
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [deleting, setDeleting] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit();
  };

  const handleDelete = async () => {
    try {
      setDeleting(true);
      await DatabaseService.deleteNote(note.id);
      
      dispatch(addNotification({
        type: 'success',
        title: '删除成功',
        message: '笔记已删除',
      }));
      
      onDelete();
    } catch (error) {
      console.error('Failed to delete note:', error);
      dispatch(addNotification({
        type: 'error',
        title: '删除失败',
        message: '删除笔记时出现错误',
      }));
    } finally {
      setDeleting(false);
      handleMenuClose();
    }
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 8) return '#4CAF50'; // 绿色
    if (mood >= 6) return '#FFC107'; // 黄色
    if (mood >= 4) return '#FF9800'; // 橙色
    return '#F44336'; // 红色
  };

  const getMoodText = (mood: number) => {
    if (mood >= 8) return '心情很好';
    if (mood >= 6) return '心情不错';
    if (mood >= 4) return '心情一般';
    return '心情不佳';
  };

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ type: 'spring', stiffness: 300 }}
    >
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
        }}
        onClick={onEdit}
      >
        {deleting && (
          <LinearProgress
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 1,
            }}
          />
        )}

        <CardContent sx={{ flex: 1, pb: 1 }}>
          {/* 标题和菜单 */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
            <Typography
              variant="h6"
              sx={{
                flex: 1,
                fontWeight: 'bold',
                lineHeight: 1.3,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {note.title || '无标题'}
            </Typography>
            
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleMenuOpen(e);
              }}
              sx={{ ml: 1 }}
            >
              <MoreIcon />
            </IconButton>
          </Box>

          {/* 内容预览 */}
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              lineHeight: 1.5,
            }}
          >
            {stringUtils.truncate(note.content.replace(/[#*`]/g, ''), 120)}
          </Typography>

          {/* 心情指数 */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                心情指数:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 'bold',
                  color: getMoodColor(note.mood),
                }}
              >
                {note.mood}/10
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={(note.mood / 10) * 100}
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  bgcolor: getMoodColor(note.mood),
                  borderRadius: 3,
                },
              }}
            />
            <Typography
              variant="caption"
              sx={{
                color: getMoodColor(note.mood),
                fontSize: '0.7rem',
                mt: 0.5,
                display: 'block',
              }}
            >
              {getMoodText(note.mood)}
            </Typography>
          </Box>

          {/* 情绪标签 */}
          {note.emotion && note.emotion.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {note.emotion.slice(0, 3).map((emotion) => (
                  <Chip
                    key={emotion.id}
                    label={emotion.name}
                    size="small"
                    sx={{
                      bgcolor: emotion.color,
                      color: 'white',
                      fontSize: '0.7rem',
                      height: 20,
                    }}
                  />
                ))}
                {note.emotion.length > 3 && (
                  <Chip
                    label={`+${note.emotion.length - 3}`}
                    size="small"
                    variant="outlined"
                    sx={{
                      fontSize: '0.7rem',
                      height: 20,
                    }}
                  />
                )}
              </Box>
            </Box>
          )}

          {/* 附加信息 */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            {/* 位置信息 */}
            {note.location && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocationIcon sx={{ fontSize: 14, color: 'text.secondary', mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  {note.location.address || `${note.location.latitude.toFixed(2)}, ${note.location.longitude.toFixed(2)}`}
                </Typography>
              </Box>
            )}

            {/* 天气信息 */}
            {note.weather && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WeatherIcon sx={{ fontSize: 14, color: 'text.secondary', mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  {note.weather.condition} {note.weather.temperature}°C
                </Typography>
              </Box>
            )}

            {/* 时间信息 */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TimeIcon sx={{ fontSize: 14, color: 'text.secondary', mr: 0.5 }} />
              <Typography variant="caption" color="text.secondary">
                {dateUtils.format(note.createdAt, 'MM-dd HH:mm')}
              </Typography>
            </Box>
          </Box>
        </CardContent>

        {/* 标签 */}
        {note.tags && note.tags.length > 0 && (
          <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {note.tags.slice(0, 2).map((tag) => (
                <Chip
                  key={tag}
                  label={`#${tag}`}
                  size="small"
                  variant="outlined"
                  sx={{
                    fontSize: '0.7rem',
                    height: 18,
                    color: 'primary.main',
                    borderColor: 'primary.main',
                  }}
                />
              ))}
              {note.tags.length > 2 && (
                <Typography variant="caption" color="text.secondary">
                  +{note.tags.length - 2}
                </Typography>
              )}
            </Box>
          </CardActions>
        )}

        {/* 菜单 */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleEdit}>
            <EditIcon sx={{ mr: 1, fontSize: 18 }} />
            编辑
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1, fontSize: 18 }} />
            删除
          </MenuItem>
        </Menu>
      </Card>
    </motion.div>
  );
};
