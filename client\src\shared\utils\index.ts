// 简化的工具函数
export const dateUtils = {
  format: (date: Date, format: string) => {
    return date.toLocaleDateString();
  },
  isToday: (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  },
};

export const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};