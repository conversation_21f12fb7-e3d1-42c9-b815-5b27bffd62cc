import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Search as SearchIcon,
  BookmarkBorder as BookmarkIcon,
  Bookmark as BookmarkedIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector } from 'react-redux';

import { RootState } from '../../store';
import { dateUtils } from '../../shared/utils';

interface JournalEntry {
  id: string;
  title: string;
  content: string;
  mood: number;
  tags: string[];
  isBookmarked: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const Journal: React.FC = () => {
  const { profile } = useSelector((state: RootState) => state.user);
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [selectedEntry, setSelectedEntry] = useState<JournalEntry | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [entryForm, setEntryForm] = useState({
    title: '',
    content: '',
    mood: 5,
    tags: [] as string[],
  });
  const [tagInput, setTagInput] = useState('');

  useEffect(() => {
    loadEntries();
  }, [profile?.id]);

  const loadEntries = () => {
    if (!profile?.id) return;
    
    try {
      const stored = localStorage.getItem(`journal_${profile.id}`);
      if (stored) {
        const parsedEntries = JSON.parse(stored).map((entry: any) => ({
          ...entry,
          createdAt: new Date(entry.createdAt),
          updatedAt: new Date(entry.updatedAt),
        }));
        setEntries(parsedEntries);
      }
    } catch (error) {
      console.error('Failed to load journal entries:', error);
    }
  };

  const saveEntries = (updatedEntries: JournalEntry[]) => {
    if (!profile?.id) return;
    
    try {
      localStorage.setItem(`journal_${profile.id}`, JSON.stringify(updatedEntries));
      setEntries(updatedEntries);
    } catch (error) {
      console.error('Failed to save journal entries:', error);
    }
  };

  const handleNewEntry = () => {
    setSelectedEntry(null);
    setEntryForm({
      title: '',
      content: '',
      mood: 5,
      tags: [],
    });
    setTagInput('');
    setShowEditor(true);
  };

  const handleEditEntry = (entry: JournalEntry) => {
    setSelectedEntry(entry);
    setEntryForm({
      title: entry.title,
      content: entry.content,
      mood: entry.mood,
      tags: [...entry.tags],
    });
    setTagInput('');
    setShowEditor(true);
  };

  const handleSaveEntry = () => {
    if (!entryForm.title.trim() || !entryForm.content.trim() || !profile?.id) return;

    const now = new Date();

    if (selectedEntry) {
      // 更新现有条目
      const updatedEntries = entries.map(entry =>
        entry.id === selectedEntry.id
          ? {
              ...entry,
              title: entryForm.title.trim(),
              content: entryForm.content.trim(),
              mood: entryForm.mood,
              tags: entryForm.tags,
              updatedAt: now,
            }
          : entry
      );
      saveEntries(updatedEntries);
    } else {
      // 创建新条目
      const newEntry: JournalEntry = {
        id: Date.now().toString(),
        title: entryForm.title.trim(),
        content: entryForm.content.trim(),
        mood: entryForm.mood,
        tags: entryForm.tags,
        isBookmarked: false,
        createdAt: now,
        updatedAt: now,
      };

      const updatedEntries = [newEntry, ...entries];
      saveEntries(updatedEntries);
    }

    setShowEditor(false);
  };

  const handleDeleteEntry = (id: string) => {
    const updatedEntries = entries.filter(entry => entry.id !== id);
    saveEntries(updatedEntries);
    setShowEditor(false);
  };

  const handleToggleBookmark = (id: string) => {
    const updatedEntries = entries.map(entry =>
      entry.id === id ? { ...entry, isBookmarked: !entry.isBookmarked } : entry
    );
    saveEntries(updatedEntries);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !entryForm.tags.includes(tagInput.trim())) {
      setEntryForm(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setEntryForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const getMoodColor = (mood: number) => {
    if (mood >= 8) return '#4CAF50';
    if (mood >= 6) return '#FFC107';
    if (mood >= 4) return '#FF9800';
    return '#F44336';
  };

  const getMoodText = (mood: number) => {
    if (mood >= 8) return '心情很好';
    if (mood >= 6) return '心情不错';
    if (mood >= 4) return '心情一般';
    return '心情不佳';
  };

  // 过滤条目
  const filteredEntries = entries.filter(entry => {
    const searchLower = searchQuery.toLowerCase();
    return (
      entry.title.toLowerCase().includes(searchLower) ||
      entry.content.toLowerCase().includes(searchLower) ||
      entry.tags.some(tag => tag.toLowerCase().includes(searchLower))
    );
  });

  return (
    <Box sx={{ p: 3, height: '60vh', display: 'flex', flexDirection: 'column' }}>
      {/* 标题和搜索 */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
          📔 日记本
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleNewEntry}
          size="small"
        >
          写日记
        </Button>
      </Box>

      {/* 搜索框 */}
      <TextField
        fullWidth
        size="small"
        placeholder="搜索日记..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        InputProps={{
          startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
        }}
        sx={{ mb: 2 }}
      />

      {/* 日记列表 */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {filteredEntries.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              {searchQuery ? '没有找到匹配的日记' : '还没有写过日记'}
            </Typography>
            <Typography variant="body2" sx={{ mb: 3 }}>
              {searchQuery ? '尝试其他关键词' : '开始记录您的生活点滴吧'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleNewEntry}
            >
              写第一篇日记
            </Button>
          </Box>
        ) : (
          <List dense>
            <AnimatePresence>
              {filteredEntries.map((entry, index) => (
                <motion.div
                  key={entry.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05, duration: 0.3 }}
                >
                  <Paper sx={{ mb: 1, overflow: 'hidden' }}>
                    <ListItemButton onClick={() => handleEditEntry(entry)}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                              {entry.title}
                            </Typography>
                            {entry.isBookmarked && (
                              <BookmarkedIcon sx={{ color: 'warning.main', fontSize: 16 }} />
                            )}
                            <Chip
                              size="small"
                              label={getMoodText(entry.mood)}
                              sx={{
                                bgcolor: getMoodColor(entry.mood),
                                color: 'white',
                                fontSize: '0.7rem',
                                height: 20,
                              }}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                mb: 1,
                              }}
                            >
                              {entry.content}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                {entry.tags.slice(0, 3).map((tag) => (
                                  <Chip
                                    key={tag}
                                    label={`#${tag}`}
                                    size="small"
                                    variant="outlined"
                                    sx={{ fontSize: '0.7rem', height: 18 }}
                                  />
                                ))}
                                {entry.tags.length > 3 && (
                                  <Typography variant="caption" color="text.secondary">
                                    +{entry.tags.length - 3}
                                  </Typography>
                                )}
                              </Box>
                              
                              <Typography variant="caption" color="text.secondary">
                                {dateUtils.format(entry.createdAt, 'MM-dd HH:mm')}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                      
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleBookmark(entry.id);
                        }}
                        size="small"
                      >
                        {entry.isBookmarked ? (
                          <BookmarkedIcon sx={{ color: 'warning.main' }} />
                        ) : (
                          <BookmarkIcon />
                        )}
                      </IconButton>
                    </ListItemButton>
                  </Paper>
                </motion.div>
              ))}
            </AnimatePresence>
          </List>
        )}
      </Box>

      {/* 编辑对话框 */}
      <Dialog open={showEditor} onClose={() => setShowEditor(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedEntry ? '编辑日记' : '写日记'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              fullWidth
              label="标题"
              value={entryForm.title}
              onChange={(e) => setEntryForm(prev => ({ ...prev, title: e.target.value }))}
            />
            
            <TextField
              fullWidth
              multiline
              rows={8}
              label="内容"
              value={entryForm.content}
              onChange={(e) => setEntryForm(prev => ({ ...prev, content: e.target.value }))}
              placeholder="记录今天的想法和感受..."
            />
            
            {/* 心情指数 */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                心情指数: {entryForm.mood}/10 - {getMoodText(entryForm.mood)}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                  <Button
                    key={value}
                    variant={entryForm.mood === value ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => setEntryForm(prev => ({ ...prev, mood: value }))}
                    sx={{
                      minWidth: 32,
                      height: 32,
                      bgcolor: entryForm.mood === value ? getMoodColor(value) : 'transparent',
                      borderColor: getMoodColor(value),
                      color: entryForm.mood === value ? 'white' : getMoodColor(value),
                      '&:hover': {
                        bgcolor: getMoodColor(value),
                        color: 'white',
                      },
                    }}
                  >
                    {value}
                  </Button>
                ))}
              </Box>
            </Box>
            
            {/* 标签 */}
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                标签
              </Typography>
              
              {entryForm.tags.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {entryForm.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={`#${tag}`}
                      onDelete={() => handleRemoveTag(tag)}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              )}
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  size="small"
                  placeholder="添加标签..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                  sx={{ flex: 1 }}
                />
                <Button onClick={handleAddTag} variant="outlined" size="small">
                  添加
                </Button>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          {selectedEntry && (
            <Button
              onClick={() => handleDeleteEntry(selectedEntry.id)}
              color="error"
              startIcon={<DeleteIcon />}
            >
              删除
            </Button>
          )}
          <Button onClick={() => setShowEditor(false)}>取消</Button>
          <Button 
            onClick={handleSaveEntry} 
            variant="contained"
            startIcon={<SaveIcon />}
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
