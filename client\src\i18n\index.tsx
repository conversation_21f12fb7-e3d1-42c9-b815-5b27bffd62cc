import React, { createContext, useContext, useEffect, useState } from 'react';
import i18n from 'i18next';
import { initReactI18next, useTranslation } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 语言资源
import zhCN from './locales/zh-CN.json';
import enUS from './locales/en-US.json';
import jaJP from './locales/ja-JP.json';

// 语言资源配置
const resources = {
  'zh-CN': {
    translation: zhCN,
  },
  'en-US': {
    translation: enUS,
  },
  'ja-JP': {
    translation: jaJP,
  },
};

// 初始化i18n
export const initializeI18n = async () => {
  await i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      fallbackLng: 'zh-CN',
      debug: process.env.NODE_ENV === 'development',
      
      interpolation: {
        escapeValue: false, // React已经默认转义
      },
      
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
        lookupLocalStorage: 'lengshang_language',
      },
      
      react: {
        useSuspense: false,
      },
    });
};

// 语言上下文
interface LanguageContextType {
  currentLanguage: string;
  changeLanguage: (language: string) => void;
  availableLanguages: { code: string; name: string; nativeName: string }[];
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// 可用语言列表
const availableLanguages = [
  { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
  { code: 'en-US', name: 'English (US)', nativeName: 'English' },
  { code: 'ja-JP', name: 'Japanese', nativeName: '日本語' },
];

// 语言提供者组件
export const I18nProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'zh-CN');

  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setCurrentLanguage(lng);
    };

    i18n.on('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, []);

  const changeLanguage = async (language: string) => {
    try {
      await i18n.changeLanguage(language);
      setCurrentLanguage(language);
      
      // 保存到本地存储
      if (window.secureStorage) {
        window.secureStorage.setItem('lengshang_language', language);
      }
      
      // 更新HTML lang属性
      document.documentElement.lang = language;
      
      console.log('Language changed to:', language);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const contextValue: LanguageContextType = {
    currentLanguage,
    changeLanguage,
    availableLanguages,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// 使用语言上下文的Hook
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within an I18nProvider');
  }
  return context;
};

// 翻译Hook的封装
export const useI18n = () => {
  const { t, i18n } = useTranslation();
  const { currentLanguage, changeLanguage, availableLanguages } = useLanguage();

  return {
    t,
    i18n,
    currentLanguage,
    changeLanguage,
    availableLanguages,
    isRTL: i18n.dir() === 'rtl',
  };
};

// 翻译组件
interface TransProps {
  i18nKey: string;
  values?: Record<string, any>;
  components?: Record<string, React.ReactElement>;
}

export const Trans: React.FC<TransProps> = ({ i18nKey, values, components }) => {
  const { t } = useTranslation();
  
  return (
    <span>
      {t(i18nKey, { ...values, ...components })}
    </span>
  );
};

// 格式化工具
export const formatUtils = {
  // 格式化数字
  number: (value: number, locale?: string) => {
    const currentLocale = locale || i18n.language || 'zh-CN';
    return new Intl.NumberFormat(currentLocale).format(value);
  },

  // 格式化货币
  currency: (value: number, currency: string = 'CNY', locale?: string) => {
    const currentLocale = locale || i18n.language || 'zh-CN';
    return new Intl.NumberFormat(currentLocale, {
      style: 'currency',
      currency,
    }).format(value);
  },

  // 格式化日期
  date: (date: Date, options?: Intl.DateTimeFormatOptions, locale?: string) => {
    const currentLocale = locale || i18n.language || 'zh-CN';
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(date);
  },

  // 格式化时间
  time: (date: Date, options?: Intl.DateTimeFormatOptions, locale?: string) => {
    const currentLocale = locale || i18n.language || 'zh-CN';
    const defaultOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
    };
    return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(date);
  },

  // 格式化相对时间
  relativeTime: (date: Date, locale?: string) => {
    const currentLocale = locale || i18n.language || 'zh-CN';
    const rtf = new Intl.RelativeTimeFormat(currentLocale, { numeric: 'auto' });
    
    const now = new Date();
    const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);
    
    if (Math.abs(diffInSeconds) < 60) {
      return rtf.format(diffInSeconds, 'second');
    }
    
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (Math.abs(diffInMinutes) < 60) {
      return rtf.format(diffInMinutes, 'minute');
    }
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (Math.abs(diffInHours) < 24) {
      return rtf.format(diffInHours, 'hour');
    }
    
    const diffInDays = Math.floor(diffInHours / 24);
    return rtf.format(diffInDays, 'day');
  },
};

// 语言切换组件
export const LanguageSwitcher: React.FC<{
  variant?: 'select' | 'menu' | 'buttons';
  className?: string;
}> = ({ variant = 'select', className }) => {
  const { currentLanguage, changeLanguage, availableLanguages } = useLanguage();
  const { t } = useTranslation();

  if (variant === 'select') {
    return (
      <select
        value={currentLanguage}
        onChange={(e) => changeLanguage(e.target.value)}
        className={className}
        aria-label={t('common.selectLanguage')}
      >
        {availableLanguages.map((lang) => (
          <option key={lang.code} value={lang.code}>
            {lang.nativeName}
          </option>
        ))}
      </select>
    );
  }

  if (variant === 'buttons') {
    return (
      <div className={className}>
        {availableLanguages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => changeLanguage(lang.code)}
            className={currentLanguage === lang.code ? 'active' : ''}
            aria-label={`${t('common.switchTo')} ${lang.nativeName}`}
          >
            {lang.nativeName}
          </button>
        ))}
      </div>
    );
  }

  // 默认返回当前语言显示
  return (
    <span className={className}>
      {availableLanguages.find(lang => lang.code === currentLanguage)?.nativeName}
    </span>
  );
};

export default i18n;
