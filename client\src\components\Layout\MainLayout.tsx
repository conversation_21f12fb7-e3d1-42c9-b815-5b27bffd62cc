import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  Psychology as PsychologyIcon,
  Quiz as QuizIcon,
  PhotoLibrary as PhotoIcon,
  Build as ToolboxIcon,
  ViewInAr as VRIcon,
  Map as MapIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Logout as LogoutIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';

import { RootState } from '../../store';
import { logout } from '../../store/slices/userSlice';
import { useI18n } from '../../i18n';
import { SidebarItem } from './SidebarItem';
import { NotificationPanel } from '../Notifications/NotificationPanel';

const drawerWidth = 280;

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { t } = useI18n();
  
  const { profile } = useSelector((state: RootState) => state.user);
  const { notifications } = useSelector((state: RootState) => state.app);
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [notificationOpen, setNotificationOpen] = useState(false);

  // 侧边栏菜单项
  const menuItems = [
    { path: '/', icon: <HomeIcon />, label: t('navigation.home') },
    { path: '/notes', icon: <PsychologyIcon />, label: t('navigation.notes') },
    { path: '/tests', icon: <QuizIcon />, label: t('navigation.tests') },
    { path: '/gallery', icon: <PhotoIcon />, label: t('navigation.gallery') },
    { path: '/toolbox', icon: <ToolboxIcon />, label: t('navigation.toolbox') },
    { path: '/vr', icon: <VRIcon />, label: t('navigation.vr') },
    { path: '/gis', icon: <MapIcon />, label: t('navigation.gis') },
    { path: '/settings', icon: <SettingsIcon />, label: t('navigation.settings') },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    dispatch(logout());
    handleUserMenuClose();
    navigate('/login');
  };

  const handleNotificationToggle = () => {
    setNotificationOpen(!notificationOpen);
  };

  // 未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length;

  // 侧边栏内容
  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo区域 */}
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2,
              color: 'white',
              fontSize: '1.5rem',
              fontWeight: 'bold',
            }}
          >
            心
          </Box>
        </motion.div>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          LENGSHANG
        </Typography>
        <Typography variant="body2" color="text.secondary">
          心理分析应用
        </Typography>
      </Box>

      <Divider />

      {/* 导航菜单 */}
      <List sx={{ flex: 1, px: 2, py: 1 }}>
        {menuItems.map((item, index) => (
          <motion.div
            key={item.path}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: index * 0.1, duration: 0.3 }}
          >
            <SidebarItem
              icon={item.icon}
              label={item.label}
              path={item.path}
              isActive={location.pathname === item.path || 
                       (item.path !== '/' && location.pathname.startsWith(item.path))}
              onClick={() => {
                navigate(item.path);
                setMobileOpen(false);
              }}
            />
          </motion.div>
        ))}
      </List>

      {/* 用户信息 */}
      <Box sx={{ p: 2 }}>
        <Divider sx={{ mb: 2 }} />
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            borderRadius: 2,
            bgcolor: 'background.paper',
            cursor: 'pointer',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
          onClick={handleUserMenuOpen}
        >
          <Avatar
            src={profile?.avatar}
            sx={{ width: 40, height: 40, mr: 2 }}
          >
            {profile?.username?.charAt(0).toUpperCase()}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle2" noWrap>
              {profile?.username || '用户'}
            </Typography>
            <Typography variant="body2" color="text.secondary" noWrap>
              {profile?.email || '<EMAIL>'}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* 顶部应用栏 */}
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(10px)',
          color: 'text.primary',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => 
              location.pathname === item.path || 
              (item.path !== '/' && location.pathname.startsWith(item.path))
            )?.label || t('navigation.home')}
          </Typography>

          {/* 通知按钮 */}
          <Tooltip title="通知">
            <IconButton
              color="inherit"
              onClick={handleNotificationToggle}
              sx={{ mr: 1 }}
            >
              <Badge badgeContent={unreadCount} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* 用户菜单按钮 */}
          <Tooltip title="用户菜单">
            <IconButton
              color="inherit"
              onClick={handleUserMenuOpen}
            >
              <AccountIcon />
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      {/* 侧边栏 */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        {/* 移动端抽屉 */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // 更好的移动端性能
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          {drawerContent}
        </Drawer>

        {/* 桌面端抽屉 */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: '1px solid rgba(0,0,0,0.1)',
            },
          }}
          open
        >
          {drawerContent}
        </Drawer>
      </Box>

      {/* 主内容区域 */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          height: '100vh',
          overflow: 'hidden',
        }}
      >
        <Toolbar /> {/* 为顶部栏留出空间 */}
        <Box sx={{ height: 'calc(100vh - 64px)', overflow: 'auto' }}>
          {children}
        </Box>
      </Box>

      {/* 用户菜单 */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { navigate('/settings'); handleUserMenuClose(); }}>
          <SettingsIcon sx={{ mr: 2 }} />
          {t('common.settings')}
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <LogoutIcon sx={{ mr: 2 }} />
          {t('common.logout')}
        </MenuItem>
      </Menu>

      {/* 通知面板 */}
      <NotificationPanel
        open={notificationOpen}
        onClose={() => setNotificationOpen(false)}
      />
    </Box>
  );
};
