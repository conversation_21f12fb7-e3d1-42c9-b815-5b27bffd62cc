import React from 'react';

// 简化的国际化配置
export const initializeI18n = async (): Promise<void> => {
  console.log('I18n initialized (simplified)');
  return Promise.resolve();
};

export const t = (key: string, defaultValue?: string): string => {
  return defaultValue || key;
};

export const useI18n = () => {
  return { t };
};

export const I18nProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return React.createElement(React.Fragment, null, children);
};

export default {
  initializeI18n,
  t,
  useI18n,
  I18nProvider,
};