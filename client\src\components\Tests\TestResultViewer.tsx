import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  Paper,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Close as CloseIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Psychology as PsychologyIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';

import { TestResult } from '../../shared/types';
import { dateUtils } from '../../shared/utils';

interface TestResultViewerProps {
  open: boolean;
  result: TestResult | null;
  onClose: () => void;
}

export const TestResultViewer: React.FC<TestResultViewerProps> = ({
  open,
  result,
  onClose,
}) => {
  if (!result) return null;

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: '我的心理测试结果',
        text: result.result,
        url: window.location.href,
      });
    } else {
      // 复制到剪贴板
      navigator.clipboard.writeText(result.result);
    }
  };

  const handleDownload = () => {
    const content = `
心理测试结果报告

测试类型: ${getTestName(result.testId)}
完成时间: ${dateUtils.format(result.completedAt, 'YYYY-MM-DD HH:mm:ss')}
测试时长: ${Math.floor(result.duration / 60)}分${result.duration % 60}秒

测试结果:
${result.result}

详细分析:
${JSON.stringify(result.scores, null, 2)}
    `;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `心理测试结果_${dateUtils.format(result.completedAt, 'YYYY-MM-DD')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getTestName = (testId: string) => {
    const names = {
      'mbti': 'MBTI 人格测试',
      'big-five': '大五人格测试',
      'depression-sds': '抑郁自评量表',
      'anxiety-sas': '焦虑自评量表',
      'stress-test': '压力测试量表',
      'emotional-intelligence': '情商测试',
    };
    return names[testId] || '心理测试';
  };

  const getTestColor = (testId: string) => {
    const colors = {
      'mbti': '#87CEEB',
      'big-five': '#FFB6C1',
      'depression-sds': '#98FB98',
      'anxiety-sas': '#DDA0DD',
      'stress-test': '#F0E68C',
      'emotional-intelligence': '#FFA07A',
    };
    return colors[testId] || '#87CEEB';
  };

  const renderMBTIResult = () => {
    if (result.testId !== 'mbti') return null;

    const { type, E, I, S, N, T, F, J, P } = result.scores;
    
    const data = [
      { dimension: '外向性', value: (E / (E + I)) * 100 },
      { dimension: '感知性', value: (S / (S + N)) * 100 },
      { dimension: '思考性', value: (T / (T + F)) * 100 },
      { dimension: '判断性', value: (J / (J + P)) * 100 },
    ];

    return (
      <Box>
        <Typography variant="h4" sx={{ textAlign: 'center', mb: 2, color: getTestColor(result.testId) }}>
          {type}
        </Typography>
        <Typography variant="h6" sx={{ textAlign: 'center', mb: 3, color: 'text.secondary' }}>
          {result.result}
        </Typography>
        
        <ResponsiveContainer width="100%" height={300}>
          <RadarChart data={data}>
            <PolarGrid />
            <PolarAngleAxis dataKey="dimension" />
            <PolarRadiusAxis domain={[0, 100]} />
            <Radar
              name="人格特征"
              dataKey="value"
              stroke={getTestColor(result.testId)}
              fill={getTestColor(result.testId)}
              fillOpacity={0.3}
            />
          </RadarChart>
        </ResponsiveContainer>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6">{E > I ? 'E' : 'I'}</Typography>
              <Typography variant="body2" color="text.secondary">
                {E > I ? '外向型' : '内向型'}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={E > I ? (E / (E + I)) * 100 : (I / (E + I)) * 100}
                sx={{ mt: 1 }}
              />
            </Paper>
          </Grid>
          <Grid item xs={6}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6">{S > N ? 'S' : 'N'}</Typography>
              <Typography variant="body2" color="text.secondary">
                {S > N ? '感觉型' : '直觉型'}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={S > N ? (S / (S + N)) * 100 : (N / (S + N)) * 100}
                sx={{ mt: 1 }}
              />
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  const renderBigFiveResult = () => {
    if (result.testId !== 'big-five') return null;

    const { openness, conscientiousness, extraversion, agreeableness, neuroticism } = result.scores;
    
    const data = [
      { name: '开放性', value: openness },
      { name: '尽责性', value: conscientiousness },
      { name: '外向性', value: extraversion },
      { name: '宜人性', value: agreeableness },
      { name: '神经质', value: neuroticism },
    ];

    return (
      <Box>
        <Typography variant="h5" sx={{ textAlign: 'center', mb: 3 }}>
          大五人格特征分析
        </Typography>
        
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis domain={[0, 100]} />
            <Tooltip />
            <Bar dataKey="value" fill={getTestColor(result.testId)} />
          </BarChart>
        </ResponsiveContainer>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          {data.map((item, index) => (
            <Grid item xs={12} sm={6} key={item.name}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                  {item.name}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={item.value}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getTestColor(result.testId),
                    },
                  }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {Math.round(item.value)}分
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  const renderSDSResult = () => {
    if (result.testId !== 'depression-sds') return null;

    const { totalScore, level } = result.scores;
    
    const getScoreColor = (score: number) => {
      if (score < 50) return '#4CAF50';
      if (score < 60) return '#FF9800';
      if (score < 70) return '#FF5722';
      return '#F44336';
    };

    return (
      <Box sx={{ textAlign: 'center' }}>
        <Typography variant="h4" sx={{ mb: 2, color: getScoreColor(totalScore) }}>
          {totalScore} 分
        </Typography>
        <Chip
          label={level}
          size="large"
          sx={{
            bgcolor: getScoreColor(totalScore),
            color: 'white',
            fontSize: '1rem',
            mb: 3,
          }}
        />
        
        <Box sx={{ maxWidth: 400, mx: 'auto', mb: 3 }}>
          <LinearProgress
            variant="determinate"
            value={(totalScore / 80) * 100}
            sx={{
              height: 12,
              borderRadius: 6,
              '& .MuiLinearProgress-bar': {
                backgroundColor: getScoreColor(totalScore),
              },
            }}
          />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography variant="caption">0</Typography>
            <Typography variant="caption">80</Typography>
          </Box>
        </Box>

        <Paper sx={{ p: 3, textAlign: 'left' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            结果解释
          </Typography>
          <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
            {totalScore < 50 && '您的抑郁程度在正常范围内，心理状态良好。'}
            {totalScore >= 50 && totalScore < 60 && '您可能存在轻度抑郁倾向，建议关注自己的情绪状态。'}
            {totalScore >= 60 && totalScore < 70 && '您可能存在中度抑郁症状，建议寻求专业心理帮助。'}
            {totalScore >= 70 && '您可能存在重度抑郁症状，强烈建议立即寻求专业医疗帮助。'}
          </Typography>
        </Paper>
      </Box>
    );
  };

  const renderDefaultResult = () => {
    return (
      <Box sx={{ textAlign: 'center' }}>
        <PsychologyIcon sx={{ fontSize: 64, color: getTestColor(result.testId), mb: 2 }} />
        <Typography variant="h5" sx={{ mb: 2 }}>
          {result.result}
        </Typography>
        <Paper sx={{ p: 3, textAlign: 'left' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            详细结果
          </Typography>
          <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
            {JSON.stringify(result.scores, null, 2)}
          </pre>
        </Paper>
      </Box>
    );
  };

  const renderResult = () => {
    switch (result.testId) {
      case 'mbti':
        return renderMBTIResult();
      case 'big-five':
        return renderBigFiveResult();
      case 'depression-sds':
      case 'anxiety-sas':
        return renderSDSResult();
      default:
        return renderDefaultResult();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h6">{getTestName(result.testId)}</Typography>
          <Typography variant="body2" color="text.secondary">
            完成时间: {dateUtils.format(result.completedAt, 'YYYY-MM-DD HH:mm')}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 3 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {renderResult()}
        </motion.div>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            测试时长: {Math.floor(result.duration / 60)}分{result.duration % 60}秒
          </Typography>
          <Typography variant="caption" color="text.secondary">
            * 此测试结果仅供参考，不能替代专业医疗诊断
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose}>
          关闭
        </Button>
        <Button
          onClick={handleShare}
          startIcon={<ShareIcon />}
          variant="outlined"
        >
          分享
        </Button>
        <Button
          onClick={handleDownload}
          startIcon={<DownloadIcon />}
          variant="contained"
          sx={{
            background: `linear-gradient(135deg, ${getTestColor(result.testId)}, ${getTestColor(result.testId)}dd)`,
          }}
        >
          下载报告
        </Button>
      </DialogActions>
    </Dialog>
  );
};
