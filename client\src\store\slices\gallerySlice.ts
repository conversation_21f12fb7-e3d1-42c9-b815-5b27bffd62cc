import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { MediaFile } from '../../shared/types';

interface GalleryState {
  images: MediaFile[];
  selectedImage: MediaFile | null;
  isLoading: boolean;
  error: string | null;
  viewMode: 'grid' | 'list';
  sortBy: 'date' | 'name' | 'size';
  filterBy: string[];
}

const initialState: GalleryState = {
  images: [],
  selectedImage: null,
  isLoading: false,
  error: null,
  viewMode: 'grid',
  sortBy: 'date',
  filterBy: [],
};

const gallerySlice = createSlice({
  name: 'gallery',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setImages: (state, action: PayloadAction<MediaFile[]>) => {
      state.images = action.payload;
    },
    addImage: (state, action: PayloadAction<MediaFile>) => {
      state.images.unshift(action.payload);
    },
    removeImage: (state, action: PayloadAction<string>) => {
      state.images = state.images.filter(img => img.id !== action.payload);
    },
    setSelectedImage: (state, action: PayloadAction<MediaFile | null>) => {
      state.selectedImage = action.payload;
    },
    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {
      state.viewMode = action.payload;
    },
    setSortBy: (state, action: PayloadAction<'date' | 'name' | 'size'>) => {
      state.sortBy = action.payload;
    },
    setFilterBy: (state, action: PayloadAction<string[]>) => {
      state.filterBy = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setImages,
  addImage,
  removeImage,
  setSelectedImage,
  setViewMode,
  setSortBy,
  setFilterBy,
} = gallerySlice.actions;

export default gallerySlice.reducer;
