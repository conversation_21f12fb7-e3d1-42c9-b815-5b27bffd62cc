import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Slices
import appSlice from './slices/appSlice';
import userSlice from './slices/userSlice';
import notesSlice from './slices/notesSlice';
import testsSlice from './slices/testsSlice';
import gallerySlice from './slices/gallerySlice';
import toolboxSlice from './slices/toolboxSlice';
import vrSlice from './slices/vrSlice';
import gisSlice from './slices/gisSlice';
import settingsSlice from './slices/settingsSlice';

// 持久化配置
const persistConfig = {
  key: 'lengshang-root',
  storage,
  whitelist: ['user', 'settings'], // 只持久化用户和设置数据
  blacklist: ['app'], // 不持久化应用状态
};

// 用户数据持久化配置
const userPersistConfig = {
  key: 'lengshang-user',
  storage,
  whitelist: ['preferences', 'profile'], // 只持久化偏好设置和个人资料
  blacklist: ['isLoading', 'error'], // 不持久化加载状态和错误
};

// 设置数据持久化配置
const settingsPersistConfig = {
  key: 'lengshang-settings',
  storage,
  whitelist: ['theme', 'language', 'notifications', 'privacy'], // 持久化所有设置
};

// 根reducer
const rootReducer = combineReducers({
  app: appSlice,
  user: persistReducer(userPersistConfig, userSlice),
  notes: notesSlice,
  tests: testsSlice,
  gallery: gallerySlice,
  toolbox: toolboxSlice,
  vr: vrSlice,
  gis: gisSlice,
  settings: persistReducer(settingsPersistConfig, settingsSlice),
});

// 持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// 配置store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
          'persist/FLUSH',
        ],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 持久化store
export const persistor = persistStore(store);

// 类型定义
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 开发模式下暴露store到全局
if (process.env.NODE_ENV === 'development') {
  (window as any).__REDUX_STORE__ = store;
}

// 性能监控
if (process.env.NODE_ENV === 'development') {
  store.subscribe(() => {
    const state = store.getState();
    const stateSize = JSON.stringify(state).length;
    
    if (stateSize > 1024 * 1024) { // 1MB
      console.warn('Redux state size is large:', (stateSize / 1024 / 1024).toFixed(2) + 'MB');
    }
  });
}

export default store;
