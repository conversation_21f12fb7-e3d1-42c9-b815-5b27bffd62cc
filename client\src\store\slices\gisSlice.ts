import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { EmotionHeatMapData, POI, GeoLocation } from '@shared/types';

interface GISState {
  heatMapData: EmotionHeatMapData[];
  pois: POI[];
  userLocation: GeoLocation | null;
  selectedEmotion: string | null;
  timeRange: {
    start: Date | null;
    end: Date | null;
  };
  mapSettings: {
    zoom: number;
    center: [number, number];
    layer: 'street' | 'satellite' | 'terrain';
    showHeatMap: boolean;
    showPOIs: boolean;
    showTrajectory: boolean;
  };
  isLoading: boolean;
  error: string | null;
  locationPermission: 'granted' | 'denied' | 'prompt';
}

const initialState: GISState = {
  heatMapData: [],
  pois: [],
  userLocation: null,
  selectedEmotion: null,
  timeRange: {
    start: null,
    end: null,
  },
  mapSettings: {
    zoom: 10,
    center: [39.9042, 116.4074], // 北京
    layer: 'street',
    showHeatMap: true,
    showPOIs: true,
    showTrajectory: false,
  },
  isLoading: false,
  error: null,
  locationPermission: 'prompt',
};

const gisSlice = createSlice({
  name: 'gis',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setHeatMapData: (state, action: PayloadAction<EmotionHeatMapData[]>) => {
      state.heatMapData = action.payload;
    },
    addHeatMapData: (state, action: PayloadAction<EmotionHeatMapData>) => {
      state.heatMapData.push(action.payload);
    },
    setPOIs: (state, action: PayloadAction<POI[]>) => {
      state.pois = action.payload;
    },
    addPOI: (state, action: PayloadAction<POI>) => {
      state.pois.push(action.payload);
    },
    setUserLocation: (state, action: PayloadAction<GeoLocation | null>) => {
      state.userLocation = action.payload;
    },
    setSelectedEmotion: (state, action: PayloadAction<string | null>) => {
      state.selectedEmotion = action.payload;
    },
    setTimeRange: (state, action: PayloadAction<{ start: Date | null; end: Date | null }>) => {
      state.timeRange = action.payload;
    },
    updateMapSettings: (state, action: PayloadAction<Partial<GISState['mapSettings']>>) => {
      state.mapSettings = { ...state.mapSettings, ...action.payload };
    },
    setLocationPermission: (state, action: PayloadAction<'granted' | 'denied' | 'prompt'>) => {
      state.locationPermission = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setHeatMapData,
  addHeatMapData,
  setPOIs,
  addPOI,
  setUserLocation,
  setSelectedEmotion,
  setTimeRange,
  updateMapSettings,
  setLocationPermission,
} = gisSlice.actions;

export default gisSlice.reducer;
