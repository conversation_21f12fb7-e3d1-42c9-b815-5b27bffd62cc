/* App 组件特定样式 */
.app {
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* 页面过渡动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateX(-20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 400ms ease-in-out, transform 400ms ease-in-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 400ms ease-in-out, transform 400ms ease-in-out;
}

/* 加载状态 */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #87CEEB 0%, #FFFFFF 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.app-loading .logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #87CEEB, #98FB98);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  box-shadow: 0 8px 25px rgba(135, 206, 235, 0.3);
  animation: pulse 2s infinite;
}

.app-loading .title {
  font-size: 24px;
  font-weight: 600;
  color: #2F4F4F;
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.app-loading .subtitle {
  font-size: 14px;
  color: #708090;
  margin-bottom: 30px;
  opacity: 0.8;
}

/* 错误状态 */
.app-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.app-error .error-icon {
  font-size: 64px;
  color: #f44336;
  margin-bottom: 20px;
}

.app-error .error-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.app-error .error-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  max-width: 500px;
  line-height: 1.5;
}

.app-error .error-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 主布局样式 */
.main-layout {
  display: flex;
  height: 100vh;
}

.main-layout .sidebar {
  width: 280px;
  flex-shrink: 0;
  background: #ffffff;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.main-layout .content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.main-layout .header {
  height: 64px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding: 0 20px;
  z-index: 100;
}

.main-layout .main {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
}

/* 认证布局样式 */
.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #87CEEB 0%, #FFFFFF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.auth-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
  pointer-events: none;
}

.auth-layout .auth-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout .sidebar {
    position: fixed;
    top: 0;
    left: -280px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .main-layout .sidebar.open {
    left: 0;
  }
  
  .main-layout .content {
    width: 100%;
  }
  
  .auth-layout {
    padding: 10px;
  }
  
  .auth-layout .auth-card {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .app-loading .logo {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
  
  .app-loading .title {
    font-size: 20px;
  }
  
  .app-error .error-icon {
    font-size: 48px;
  }
  
  .app-error .error-title {
    font-size: 20px;
  }
  
  .app-error .error-message {
    font-size: 14px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .main-layout .sidebar {
    background: #1e1e2e;
    border-right-color: rgba(255, 255, 255, 0.1);
  }
  
  .main-layout .header {
    background: rgba(30, 30, 46, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .main-layout .main {
    background: #11111b;
  }
  
  .auth-layout .auth-card {
    background: rgba(30, 30, 46, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .app-error {
    background: #11111b;
  }
  
  .app-error .error-title {
    color: #cdd6f4;
  }
  
  .app-error .error-message {
    color: #a6adc8;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .main-layout .sidebar {
    border-right: 2px solid currentColor;
  }
  
  .main-layout .header {
    border-bottom: 2px solid currentColor;
  }
  
  .auth-layout .auth-card {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .page-transition-enter-active,
  .page-transition-exit-active {
    transition: none;
  }
  
  .main-layout .sidebar {
    transition: none;
  }
  
  .app-loading .logo {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .main-layout .sidebar,
  .main-layout .header {
    display: none;
  }
  
  .main-layout .main {
    background: white;
  }
  
  .auth-layout {
    background: white;
  }
}
