import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Fab,
  Dialog,
  DialogContent,
  Chip,
  Menu,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Add as AddIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  MoreVert as MoreIcon,
  Favorite as FavoriteIcon,
  Share as ShareIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';

import { RootState } from '../../store';
import {
  setImages,
  addImage,
  removeImage,
  setSelectedImage,
  setViewMode,
  setSortBy,
  setFilterBy,
  setLoading,
} from '../../store/slices/gallerySlice';
import { DatabaseService } from '../../services/database';
import { addNotification } from '../../store/slices/appSlice';
import { ImageUploader } from '../../components/Gallery/ImageUploader';
import { ImageEditor } from '../../components/Gallery/ImageEditor';
import { EMOTION_TAGS } from '../../shared/constants';
import { MediaFile } from '../../shared/types';
import { dateUtils } from '../../shared/utils';

export const GalleryPage: React.FC = () => {
  const dispatch = useDispatch();
  
  const { profile } = useSelector((state: RootState) => state.user);
  const {
    images,
    selectedImage,
    isLoading,
    viewMode,
    sortBy,
    filterBy,
  } = useSelector((state: RootState) => state.gallery);

  const [showUploader, setShowUploader] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedImageForMenu, setSelectedImageForMenu] = useState<MediaFile | null>(null);

  useEffect(() => {
    if (profile?.id) {
      loadImages();
    }
  }, [profile?.id]);

  const loadImages = async () => {
    try {
      dispatch(setLoading(true));
      const mediaFiles = await DatabaseService.getMediaFiles(profile!.id);
      dispatch(setImages(mediaFiles));
    } catch (error) {
      console.error('Failed to load images:', error);
      dispatch(addNotification({
        type: 'error',
        title: '加载失败',
        message: '无法加载图片',
      }));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleImageUpload = async (files: File[]) => {
    try {
      for (const file of files) {
        const mediaFile: Omit<MediaFile, 'id'> = {
          filename: file.name,
          originalName: file.name,
          mimeType: file.type,
          size: file.size,
          url: URL.createObjectURL(file),
          thumbnailUrl: URL.createObjectURL(file),
          userId: profile!.id,
          uploadedAt: new Date(),
          metadata: {
            width: 0,
            height: 0,
            emotions: [],
            tags: [],
            location: null,
          },
        };

        const id = await DatabaseService.saveMediaFile(mediaFile);
        dispatch(addImage({ ...mediaFile, id }));
      }

      dispatch(addNotification({
        type: 'success',
        title: '上传成功',
        message: `已上传 ${files.length} 张图片`,
      }));
    } catch (error) {
      console.error('Failed to upload images:', error);
      dispatch(addNotification({
        type: 'error',
        title: '上传失败',
        message: '图片上传失败',
      }));
    }
  };

  const handleImageClick = (image: MediaFile) => {
    dispatch(setSelectedImage(image));
    setShowImageViewer(true);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, image: MediaFile) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setSelectedImageForMenu(image);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedImageForMenu(null);
  };

  const handleEditImage = () => {
    if (selectedImageForMenu) {
      dispatch(setSelectedImage(selectedImageForMenu));
      setShowEditor(true);
    }
    handleMenuClose();
  };

  const handleDeleteImage = async () => {
    if (selectedImageForMenu) {
      try {
        await DatabaseService.deleteMediaFile(selectedImageForMenu.id);
        dispatch(removeImage(selectedImageForMenu.id));
        dispatch(addNotification({
          type: 'success',
          title: '删除成功',
          message: '图片已删除',
        }));
      } catch (error) {
        console.error('Failed to delete image:', error);
        dispatch(addNotification({
          type: 'error',
          title: '删除失败',
          message: '删除图片失败',
        }));
      }
    }
    handleMenuClose();
  };

  const handleFilterChange = (emotionId: string) => {
    const newFilter = filterBy.includes(emotionId)
      ? filterBy.filter(id => id !== emotionId)
      : [...filterBy, emotionId];
    dispatch(setFilterBy(newFilter));
  };

  // 过滤和排序图片
  const filteredImages = images
    .filter(image => {
      if (filterBy.length === 0) return true;
      const imageEmotions = image.metadata?.emotions || [];
      return filterBy.some(emotionId => 
        imageEmotions.some(emotion => emotion.id === emotionId)
      );
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.filename.localeCompare(b.filename);
        case 'size':
          return b.size - a.size;
        default: // date
          return new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime();
      }
    });

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            心理相册
          </Typography>
          <Typography variant="body1" color="text.secondary">
            记录美好瞬间，标记情绪回忆
          </Typography>
        </Box>
      </motion.div>

      {/* 工具栏 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* 视图切换 */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(e, newMode) => newMode && dispatch(setViewMode(newMode))}
              size="small"
            >
              <ToggleButton value="grid">
                <GridViewIcon />
              </ToggleButton>
              <ToggleButton value="list">
                <ListViewIcon />
              </ToggleButton>
            </ToggleButtonGroup>

            {/* 排序选择 */}
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                const nextSort = sortBy === 'date' ? 'name' : sortBy === 'name' ? 'size' : 'date';
                dispatch(setSortBy(nextSort));
              }}
            >
              排序: {sortBy === 'date' ? '日期' : sortBy === 'name' ? '名称' : '大小'}
            </Button>
          </Box>

          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setShowUploader(true)}
            sx={{
              background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
              },
            }}
          >
            上传图片
          </Button>
        </Box>
      </motion.div>

      {/* 情绪筛选 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            按情绪筛选：
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {EMOTION_TAGS.map((tag) => (
              <Chip
                key={tag.id}
                label={`${tag.icon} ${tag.name}`}
                variant={filterBy.includes(tag.id) ? 'filled' : 'outlined'}
                onClick={() => handleFilterChange(tag.id)}
                sx={{
                  bgcolor: filterBy.includes(tag.id) ? tag.color : 'transparent',
                  color: filterBy.includes(tag.id) ? 'white' : 'text.primary',
                  borderColor: tag.color,
                  '&:hover': {
                    bgcolor: tag.color,
                    color: 'white',
                  },
                }}
              />
            ))}
          </Box>
        </Box>
      </motion.div>

      {/* 图片网格 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.6 }}
      >
        {filteredImages.length === 0 ? (
          <Box
            sx={{
              textAlign: 'center',
              py: 8,
              color: 'text.secondary',
            }}
          >
            <Typography variant="h6" sx={{ mb: 2 }}>
              {filterBy.length > 0 ? '没有找到匹配的图片' : '还没有上传任何图片'}
            </Typography>
            <Typography variant="body2" sx={{ mb: 3 }}>
              {filterBy.length > 0 ? '尝试调整筛选条件' : '开始上传您的第一张图片吧'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setShowUploader(true)}
              sx={{
                background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
                },
              }}
            >
              上传图片
            </Button>
          </Box>
        ) : (
          <Grid container spacing={viewMode === 'grid' ? 2 : 1}>
            <AnimatePresence>
              {filteredImages.map((image, index) => (
                <Grid 
                  item 
                  xs={12} 
                  sm={viewMode === 'grid' ? 6 : 12} 
                  md={viewMode === 'grid' ? 4 : 12} 
                  lg={viewMode === 'grid' ? 3 : 12}
                  key={image.id}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ delay: index * 0.05, duration: 0.4 }}
                    whileHover={{ y: -4 }}
                  >
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                        },
                      }}
                      onClick={() => handleImageClick(image)}
                    >
                      <CardMedia
                        component="img"
                        height={viewMode === 'grid' ? 200 : 120}
                        image={image.thumbnailUrl || image.url}
                        alt={image.filename}
                        sx={{ objectFit: 'cover' }}
                      />
                      
                      <CardContent sx={{ pb: 1 }}>
                        <Typography variant="subtitle2" noWrap>
                          {image.originalName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {dateUtils.format(image.uploadedAt, 'MM-dd HH:mm')}
                        </Typography>
                        
                        {/* 情绪标签 */}
                        {image.metadata?.emotions && image.metadata.emotions.length > 0 && (
                          <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {image.metadata.emotions.slice(0, 2).map((emotion) => (
                              <Chip
                                key={emotion.id}
                                label={emotion.name}
                                size="small"
                                sx={{
                                  bgcolor: emotion.color,
                                  color: 'white',
                                  fontSize: '0.7rem',
                                  height: 18,
                                }}
                              />
                            ))}
                          </Box>
                        )}
                      </CardContent>

                      <CardActions sx={{ pt: 0, justifyContent: 'space-between' }}>
                        <Box>
                          <IconButton size="small">
                            <FavoriteIcon fontSize="small" />
                          </IconButton>
                          <IconButton size="small">
                            <ShareIcon fontSize="small" />
                          </IconButton>
                        </Box>
                        
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, image)}
                        >
                          <MoreIcon fontSize="small" />
                        </IconButton>
                      </CardActions>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </AnimatePresence>
          </Grid>
        )}
      </motion.div>

      {/* 上传按钮 */}
      <Fab
        color="primary"
        aria-label="upload image"
        onClick={() => setShowUploader(true)}
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
          },
        }}
      >
        <AddIcon />
      </Fab>

      {/* 图片上传器 */}
      <ImageUploader
        open={showUploader}
        onClose={() => setShowUploader(false)}
        onUpload={handleImageUpload}
      />

      {/* 图片编辑器 */}
      <ImageEditor
        open={showEditor}
        image={selectedImage}
        onClose={() => setShowEditor(false)}
        onSave={loadImages}
      />

      {/* 图片查看器 */}
      <Dialog
        open={showImageViewer}
        onClose={() => setShowImageViewer(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogContent sx={{ p: 0, position: 'relative' }}>
          <IconButton
            onClick={() => setShowImageViewer(false)}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0,0,0,0.5)',
              color: 'white',
              zIndex: 1,
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.7)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
          {selectedImage && (
            <img
              src={selectedImage.url}
              alt={selectedImage.filename}
              style={{
                width: '100%',
                height: 'auto',
                maxHeight: '80vh',
                objectFit: 'contain',
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* 右键菜单 */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEditImage}>
          <EditIcon sx={{ mr: 1 }} />
          编辑
        </MenuItem>
        <MenuItem onClick={handleDeleteImage} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          删除
        </MenuItem>
      </Menu>
    </Container>
  );
};
