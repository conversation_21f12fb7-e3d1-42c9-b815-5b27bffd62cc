import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Home as HomeIcon, ArrowBack as BackIcon } from '@mui/icons-material';

export const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Container
      maxWidth="md"
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        py: 4,
      }}
    >
      {/* 404动画 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, type: 'spring' }}
      >
        <Typography
          variant="h1"
          sx={{
            fontSize: { xs: '6rem', md: '8rem' },
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #87CEEB, #98FB98)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            mb: 2,
          }}
        >
          404
        </Typography>
      </motion.div>

      {/* 错误信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        <Typography
          variant="h4"
          sx={{
            fontWeight: 'bold',
            color: 'text.primary',
            mb: 2,
          }}
        >
          页面未找到
        </Typography>
        
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{ mb: 4, maxWidth: 500 }}
        >
          抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
          请检查URL是否正确，或返回首页继续浏览。
        </Typography>
      </motion.div>

      {/* 操作按钮 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.6 }}
      >
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
          <Button
            variant="contained"
            size="large"
            startIcon={<HomeIcon />}
            onClick={handleGoHome}
            sx={{
              background: 'linear-gradient(135deg, #87CEEB 0%, #98FB98 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5BA3D0 0%, #7BC97B 100%)',
              },
            }}
          >
            返回首页
          </Button>
          
          <Button
            variant="outlined"
            size="large"
            startIcon={<BackIcon />}
            onClick={handleGoBack}
            sx={{
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                borderColor: 'primary.dark',
                bgcolor: 'primary.light',
              },
            }}
          >
            返回上页
          </Button>
        </Box>
      </motion.div>

      {/* 装饰性元素 */}
      <Box
        sx={{
          position: 'absolute',
          top: '20%',
          left: '10%',
          width: 100,
          height: 100,
          borderRadius: '50%',
          background: 'linear-gradient(135deg, rgba(135, 206, 235, 0.1), rgba(152, 251, 152, 0.1))',
          zIndex: -1,
        }}
      />
      
      <Box
        sx={{
          position: 'absolute',
          bottom: '20%',
          right: '15%',
          width: 150,
          height: 150,
          borderRadius: '50%',
          background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(221, 160, 221, 0.1))',
          zIndex: -1,
        }}
      />

      {/* 浮动动画元素 */}
      <motion.div
        style={{
          position: 'absolute',
          top: '30%',
          right: '20%',
          width: 60,
          height: 60,
          borderRadius: '50%',
          background: 'rgba(135, 206, 235, 0.2)',
          zIndex: -1,
        }}
        animate={{
          y: [0, -20, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <motion.div
        style={{
          position: 'absolute',
          bottom: '30%',
          left: '20%',
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: 'rgba(152, 251, 152, 0.2)',
          zIndex: -1,
        }}
        animate={{
          y: [0, 15, 0],
          scale: [1, 0.9, 1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1,
        }}
      />

      {/* 心理健康提示 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.8 }}
        style={{ marginTop: '3rem' }}
      >
        <Box
          sx={{
            p: 3,
            borderRadius: 2,
            background: 'rgba(135, 206, 235, 0.1)',
            border: '1px solid rgba(135, 206, 235, 0.3)',
            maxWidth: 400,
          }}
        >
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontStyle: 'italic' }}
          >
            💡 小贴士：迷路并不可怕，重要的是找到回家的路。
            就像心理健康一样，偶尔的困惑是正常的，关键是要有勇气寻求帮助和方向。
          </Typography>
        </Box>
      </motion.div>
    </Container>
  );
};
